<template>
  <div class="mod-bga__phytosampledata">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
      <!--查询-->
      <el-form-item>
        <el-date-picker v-model="state.dataForm.monitorTime" type="date" value-format="YYYY-MM-DD" placeholder="监测日期"> </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-input style="width: 300px" v-model="state.dataForm.stationName" :placeholder="$t('zhdc.stationName')" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button @click="state.getDataList()">{{ $t("query") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column width="150" prop="stationCode" :label="$t('phytoplankto.stationCode')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="stationName" :label="$t('phytoplankto.stationName')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="longitude" :label="$t('zhdc.longitude')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="latitude" :label="$t('zhdc.latitude')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="ammoniaNitrogen" :label="$t('phytoplankto.ammoniaNitrogen')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="totalPhosphorus" :label="$t('phytoplankto.totalPhosphorus')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="monitorTime" :label="$t('phytoplankto.monitorTime')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="monitorArea" :label="$t('phytoplankto.monitorArea')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="ph" :label="$t('phytoplankto.ph')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="waterTemperature" :label="$t('phytoplankto.waterTemperature')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="algalDensity" :label="$t('phytoplankto.algalDensity')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="permanganateIndex" :label="$t('phytoplankto.permanganateIndex')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="chlorophyllA" :label="$t('phytoplankto.chlorophyllA')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="totalNitrogen" :label="$t('phytoplankto.totalNitrogen')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="dissolvedOxygen" :label="$t('phytoplankto.dissolvedOxygen')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="conductivity" :label="$t('phytoplankto.conductivity')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="turbidity" :label="$t('phytoplankto.turbidity')" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
    <update-bga-data :uploadPath="state.uploadURL" ref="updateBgaDataRef" @refreshDataList="state.getDataList"></update-bga-data>
  </div>
</template>

<script lang="ts" setup>
  import useView from "@/hooks/useView";
  import { onMounted, reactive, ref, toRefs, nextTick } from "vue";
  import AddOrUpdate from "./phytosampledata-add-or-update.vue";
  import { globalLanguage } from "@/utils/globalLang";
  import UpdateBgaData from "@/components/uploadExcel/update-bga-data.vue";
  import { useBase64 } from "@vueuse/core";
  import { Buffer } from "buffer";
  import baseService from "@/service/baseService";
  //以下注释不能删除，否则会报错
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  //@ts-ignore
  import { SM4Util } from "sm4util/sm4";
  import axios from "axios";
  import { useRoute } from "vue-router"; // 导入 useRoute
  const route = useRoute(); // 使用 useRoute
  const { $t } = globalLanguage();
  const view = reactive({
    deleteIsBatch: true,
    getDataListURL: "/bga/interface_data_huti/page",
    getDataListIsPage: true,
    exportURL: "/bga/interface_data_huti/export",
    deleteURL: "/bga/interface_data_huti",
    uploadURL: "/bga/interface_data_huti/upload",
    ExcelURL: "/bga/interface_data_huti/excel",
    dataForm: {
      id: "",
      stationName: "",
      monitorTime: ""
    }
  });

  const state = reactive({ ...useView(view), ...toRefs(view) });

  // 页面加载时检查路由参数并自动查询
  onMounted(() => {
    console.log('页面加载完成，检查路由参数:', route.query);
    if (route.query.stationName) {
      state.dataForm.stationName = route.query.stationName as string;
      console.log('设置stationName:', route.query.stationName);
      // 如果是从monitoringszjhcstation页面跳转过来的，自动执行查询
      if (route.query.from === 'monitoring') {
        console.log('来自monitoring页面，执行自动查询');
        // 使用nextTick确保DOM更新后再执行查询
        nextTick(() => {
          console.log('执行查询');
          state.getDataList();
        });
      } else {
        console.log('不是来自monitoring页面，from参数为:', route.query.from);
      }
    } else {
      console.log('没有stationName参数');
    }
  });

  const addOrUpdateRef = ref();
  const addOrUpdateHandle = (id?: number) => {
    addOrUpdateRef.value.init(id);
  };
  const updateBgaDataRef = ref();
  const uploadBgaDataHandle = () => {
    updateBgaDataRef.value.init();
  };

</script>
