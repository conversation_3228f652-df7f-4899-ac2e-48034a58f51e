<template>
  <div class="mod-bga__wqgk">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="sampleNumber" label="样品编号" header-align="center" align="center"></el-table-column>
      <el-table-column prop="waterName" label="水体名称" header-align="center" align="center"></el-table-column>
      <el-table-column prop="stationName" label="监测站点名称" header-align="center" align="center"></el-table-column>
      <el-table-column prop="monitoringUnits" label="监测单位" header-align="center" align="center"></el-table-column>
      <el-table-column prop="year" label="年" header-align="center" align="center"></el-table-column>
      <el-table-column prop="month" label="月" header-align="center" align="center"></el-table-column>
      <el-table-column prop="day" label="日" header-align="center" align="center"></el-table-column>
      <el-table-column prop="time" label="具体时间" header-align="center" align="center"></el-table-column>
      <el-table-column prop="waterTemperature" label="水温" header-align="center" align="center"></el-table-column>
      <el-table-column prop="ph" label="pH无量纲" header-align="center" align="center"></el-table-column>
      <el-table-column prop="dissolvedOxygen" label="溶解氧mg/L" header-align="center" align="center"></el-table-column>
      <el-table-column prop="permanganateIndex" label="高锰酸盐指数 (mg/L)" header-align="center" align="center"></el-table-column>
      <el-table-column prop="chemicalOxygenDemand" label="化学需氧量 (mg/L)" header-align="center" align="center"></el-table-column>
      <el-table-column prop="bod5" label="五日生化需氧量(mg/L)" header-align="center" align="center"></el-table-column>
      <el-table-column prop="ammoniaNitrogen" label="氨氮(mg/L)" header-align="center" align="center"></el-table-column>
      <el-table-column prop="totalPhosphorus" label="总磷 (mg/L)" header-align="center" align="center"></el-table-column>
      <el-table-column prop="totalNitrogen" label="总氮 (mg/L)" header-align="center" align="center"></el-table-column>
      <el-table-column prop="transparency" label="透明度 (m)" header-align="center" align="center"></el-table-column>
      <el-table-column prop="chlorophyllA" label="叶绿素a (mg/L)" header-align="center" align="center"></el-table-column>
      <el-table-column prop="electricalConductivity" label="电导率 (μs/cm)" header-align="center" align="center"></el-table-column>
      <el-table-column prop="turbidity" label="浊度 (NTU)" header-align="center" align="center"></el-table-column>
      <el-table-column prop="algalBiomass" label="藻生物量（仪器）万个/L" header-align="center" align="center"></el-table-column>
      <el-table-column prop="waterQuality" label="水质类别" header-align="center" align="center"></el-table-column>
      <el-table-column prop="wpi" label="wpi指数" header-align="center" align="center"></el-table-column>
      <el-table-column prop="wpiQuality" label="wpi水质类别" header-align="center" align="center"></el-table-column>
      <el-table-column prop="comprehensivePollution" label="综合污染指数" header-align="center" align="center"></el-table-column>
      <el-table-column prop="comprehensiveNutritional" label="综合营养状态指数" header-align="center" align="center"></el-table-column>
      <el-table-column prop="nutritionalStatus" label="营养状态" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button  type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button  type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
  </div>
</template>

<script lang="ts" setup>
  import useView from "@/hooks/useView";
  import { reactive, ref, toRefs } from "vue";
  import AddOrUpdate from "./wqgk-add-or-update.vue";
  import { globalLanguage } from "@/utils/globalLang";
  const { $t } = globalLanguage();
  const view = reactive({
    deleteIsBatch: true,
    getDataListURL: "/waterquality/wqgk/page",
    getDataListIsPage: true,
    exportURL: "/waterquality/wqgk/export",
    deleteURL: "/waterquality/wqgk"
  });

  const state = reactive({ ...useView(view), ...toRefs(view) });

  const addOrUpdateRef = ref();
  const addOrUpdateHandle = (id?: number) => {
    addOrUpdateRef.value.init(id);
  };
</script>
