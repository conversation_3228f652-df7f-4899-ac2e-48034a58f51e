<template>
  <div class="mod-bga__phytoclass">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-date-picker v-model="state.dataForm.time" type="date" value-format="YYYY-MM-DD" placeholder="监测日期"> </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button @click="state.getDataList()">{{ $t("query") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column :label="$t('鸟类图像预览')" header-align="center" align="center">
        <template v-slot="scope">
          <el-image v-if="scope.row.imgPath" :src="scope.row.imgPath" fit="contain" style="width: 100%; height: 50%; cursor: pointer" @click="handlePreviewImage(scope.row.imgPath)">
            <template #error>
              <div class="image-slot">
                <el-icon><icon-picture /></el-icon>
              </div>
            </template>
          </el-image>
          <div v-else class="image-slot">
            <el-icon><icon-picture /></el-icon>
          </div>
        </template>
      </el-table-column>
      <el-table-column :label="$t('鸟类视频预览')" header-align="center" align="center">
        <template v-slot="scope">
          <el-button v-if="scope.row.mediaUrl" type="primary" link @click="handlePreviewVideo(scope.row.mediaUrl)">
            {{ $t("点击查看视频") }}
          </el-button>
          <div v-else class="image-slot">
            <el-icon><icon-video /></el-icon>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="speciesId" :label="$t('鸟类Id')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="speciesName" :label="$t('鸟类名称')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="time" :label="$t('时间')" header-align="center" align="center"></el-table-column>
      <el-table-column prop="deviceId" :label="$t('摄像头id')" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
          <el-button type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>

    <!-- 图片预览 -->
    <el-dialog v-model="previewVisible" title="图片预览" width="80%">
      <img :src="previewImage" style="max-width: 100%; height: auto" />
    </el-dialog>

    <!-- 视频预览 -->
    <el-dialog v-model="videoPreviewVisible" title="视频预览" width="80%" @closed="stopVideoPreview">
      <video ref="videoPlayer" :src="previewVideoUrl" controls style="width: 100%"></video>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./bird-add-or-update.vue";
import { globalLanguage } from "@/utils/globalLang";
import axios from "axios";

const { $t } = globalLanguage();

// 图片预览相关
const previewVisible = ref(false);
const previewImage = ref("");

const handlePreviewImage = (url: string) => {
  previewImage.value = url;
  previewVisible.value = true;
};

// 视频预览相关
const videoPreviewVisible = ref(false);
const previewVideoUrl = ref("");
const videoPlayer = ref<HTMLElement | null>(null);

const handlePreviewVideo = (url: string) => {
  previewVideoUrl.value = url;
  videoPreviewVisible.value = true;
};

const stopVideoPreview = () => {
  if (videoPlayer.value) {
    (videoPlayer.value as HTMLVideoElement).pause();
  }
};

const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/bird/birdnumbercount/page",
  getDataListIsPage: true,
  exportURL: "/bird/birdnumbercount/export",
  deleteURL: "/bird/birdnumbercount",
  dataForm: {
    id: "",
    time: ""
  }
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
</script>

<style scoped>
.image-slot {
  width: 100px;
  height: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--el-color-info);
  font-size: 24px;
}
</style>
