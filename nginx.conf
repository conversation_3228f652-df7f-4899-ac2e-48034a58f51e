user  nginx;
worker_processes  auto;

error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

events {
  worker_connections  1024;
}

http {
  include       /etc/nginx/mime.types;
  default_type  application/octet-stream;

  log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
  '$status $body_bytes_sent "$http_referer" '
  '"$http_user_agent" "$http_x_forwarded_for"';

  server {
    listen 80;
    server_name localhost;

    location / {
      root   /usr/share/nginx/html;
      index  index.html index.htm;
      try_files $uri $uri/ /index.html;
    }

    #通配符api代理
    location /api/v1 {
      proxy_pass http://bga-admin-server:9080;
    }

    location /api/v1/captcha {
      proxy_pass http://bga-admin-server:9080;
    }

    location /data-bga {
      proxy_pass http://*************:9000;
    }


    location /api/v1/upload {
      proxy_pass http://common-files.default.svc.cluster.local:9080;
      client_max_body_size  1000m;
    }
  }

  # 注意要添加这一行
  # include /etc/nginx/conf.d/*.conf;
}
