const t = {}

t.loading = 'Loading...'

t.brand = {}
t.brand.lg = 'Erhai Lake Endemic Germplasm Resources Intelligent Management Platform'
t.brand.mini = 'Germplasm Resources Intelligent Management Platform'

t.add = 'Add'
t.delete = 'Delete'
t.deleteBatch = 'Delete'
t.update = 'Update'
t.query = 'Query'
t.export = 'Export'
t.handle = 'Action'
t.confirm = 'Confirm'
t.cancel = 'Cancel'
t.clear = 'Clear'
t.logout = 'Logout'
t.manage = 'Manage'
t.noData = 'No Data'
t.createDate = 'Creation Date'
t.keyword = 'Keyword:'
t.choose = 'Please Choose'
t.details = 'Details';
t.home = 'Home'
t.uploadFile = 'Upload'

t.prompt = {}
t.prompt.title = 'Prompt'
t.prompt.info = 'Are you sure you want to perform [{handle}] operation?'
t.prompt.success = 'Operation successful'
t.prompt.failed = 'Operation failed'
t.prompt.deleteBatch = 'Please select the items to delete'


t.validate = {}
t.validate.required = 'This field is required'
t.validate.format = '{attr} format is incorrect'

t.upload = {}
t.upload.text = 'Drag the file here, or click to upload'
t.upload.tip = 'Only support {format} format files!'
t.upload.button = 'Click to upload'
t.upload.imgFormat = 'Please upload images in jpeg format'
t.upload.videoFormat = 'Please upload videos in video format'
t.upload.size = 'Size exceeds limit!'

t.datePicker = {}
t.datePicker.range = 'to'
t.datePicker.start = 'Start Date'
t.datePicker.end = 'End Date'

t.fullscreen = {}
t.fullscreen.prompt = 'Your browser does not support this operation'

t.updatePassword = {}
t.updatePassword.title = 'Change Password'
t.updatePassword.loginOut = 'Login Out'
t.updatePassword.username = 'Username'
t.updatePassword.password = 'Current Password'
t.updatePassword.newPassword = 'New Password'
t.updatePassword.confirmPassword = 'Confirm Password'
t.updatePassword.validate = {}
t.updatePassword.validate.confirmPassword = 'Confirmation password does not match the new password input'

t.contentTabs = {
    'Close Current Tab': "Close the current tab",
    'Close Other Tabs': "Close other tabs",
    'Close All Tabs': "Close all tabs"
}

t.contentTabs.closeCurrent = 'Close Current Tab'
t.contentTabs.closeOther = 'Close Other Tabs'
t.contentTabs.closeAll = 'Close All Tabs'

/* Pages */
t.notFound = {}
t.notFound.desc = 'Sorry, the page you are looking for is <em>missing</em>...'
t.notFound.back = 'Back'
t.notFound.home = 'Home'

t.login = {}
t.login.title = 'Login'
t.login.username = 'Username'
t.login.password = 'Password'
t.login.captcha = 'Captcha'
t.login.demo = 'Online Demo'
t.login.copyright = 'Blue-green Algae'
t.login.success = 'Login Success'

t.theme = {
    themeConfig: 'Theme Setting',
    darkSidebar: 'Dark Sidebar',
    lightSidebar: 'Light Sidebar',
    darkTopBar: 'Dark Top Bar',
    lightTopBar: 'Light Top Bar',
    themedTopBar: 'Themed Top Bar',
    DawnBlue: 'Dawn Blue',
    Cyan: 'Cyan',
    Blue: 'Blue',
    Green: 'Green',
    BlueGreen: 'Blue Green',
    Indigo: 'Indigo',
    Brown: 'Brown',
    Purple: 'Purple',
    Gray: 'Gray',
    Orange: 'Orange',
    MixedRed: 'Mixed Red',
    Yellow: 'Yellow',
    Red: 'Red',
    Black: 'Black',
    leftMenuLayout: 'Left Menu Layout',
    topMenuLayout: 'Top Menu Layout',
    mixedMenuLayout: 'Mixed Menu Layout',
    contentCover: 'Content Cover',
    otherConfig: 'Other Configuration',
    fixedLogoBar: 'Fixed Logo Bar',
    colorfulSidebarIcons: 'Colorful Sidebar Icons',
    exclusiveExpandSidebar: 'Exclusive Expand Sidebar',
    tagDisplayStyle: 'Tag Display Style',
    tagStyleDefault: 'Default',
    tagStyleDot: 'Dot',
    tagStyleCard: 'Card',
    layoutModes: 'Layout Modes'
};

t.address = {
    '晖湾北水域': 'Hui Wan Bei Shui Yu',
    '薰衣草庄园': 'Xun Yi Cao Zhuang Yuan',
    '金宝山': 'Jin Bao Shan',
    '西华湿地': 'Xi Hua Shi Di',
    '古滇': 'Gu Dian',
    '晖北水域': 'Hui Bei Shui Yu',
    '通河街附近水域': 'Tong He Jie Fu Jin Shui Yu',
    '滇池': 'Dian Chi',
    '四家村水域附近': 'Si Jia Cun Shui Yu Fu Jin',
    '大悲阁': 'Da Bei Ge',
    '福保路附近水域': 'Fu Bao Lu Fu Jin Shui Yu',
    '工人疗养院': 'Gong Ren Liao Yang Yuan',
    '团山': 'Tuan Shan',
    '黄金支队': 'Huang Jin Zhi Dui',
    '龙门藻站': 'Long Men Zao Zhan',
    '大咀头': 'Da Ju Tou',
    '海口红泥咀': 'Hai Kou Hong Ni Ju',
    '海口蒋凹村': 'Hai Kou Jiang Ao Cun',
    '海月路1号': 'Hai Yue Lu 1 Hao',
    '江尾村': 'Jiang Wei Cun',
    '晋宁大河尾水域': 'Jin Ning Da He Wei Shui Yu',
    '鸽子湾': 'Ge Zi Wan',
    '海口彩云湾': 'Hai Kou Cai Yun Wan',
    '晋宁河嘴水域': 'Jin Ning He Zui Shui Yu',
    '晋宁镇海阁水域': 'Jin Ning Zhen Hai Ge Shui Yu',
    '梁王': 'Liang Wang',
    '海东湿地': 'Hai Dong Shi Di',
    '王官湿地': 'Wang Guan Shi Di',
    '沙堤': 'Sha Di',
    '草海': 'Cao Hai',
    '大湾': 'Da Wan',
    '七十郎': 'Qi Shi Lang'
}

t.router = {
    '主页': 'Home',
    '工作台': 'Dashboard',
    '水文信息': 'Hydrological Information',
    '湖库水文数据': 'Lake Hydrological Data',
    '堰闸水文数据': 'Weir Hydrological Data',
    '干支流水文数据': 'Branch Hydrological Data',
    '底栖动物': 'Benthos',
    '底栖动物详细表': 'Benthos Details',
    '底栖动物门': 'Benthos Phylum',
    '底栖动物纲': 'Benthos Class',
    '底栖动物目': 'Benthos Order',
    '底栖动物科': 'Benthos Family',
    '底栖动物属': 'Benthos Genus',
    '底栖动物种': 'Benthos Type',
    '底栖动物平均生物量': 'Benthos Average Biomass',
    '底栖动物平均密度': 'Benthos Average Density',
    '底栖动物生物量': 'Benthos Biomass',
    '底栖动物名录': 'Benthos Directory',
    '底栖动物密度': 'Benthos Density',
    '底栖动物数据资源': 'Benthos Data',
    '底栖动物样品采样表': 'Benthos Sample',
    '底栖动物测试数据表': 'Benthos TestData',
    '底栖动样品统计表': 'Sample Statistics',
    '气象信息': 'Weather Information',
    '浮游动物': 'Zooplankton',
    '浮游动物密度': 'Zooplankton Density',
    '浮游动物生物量': 'Zooplankton Biomass',
    '浮游动物数据资源': 'Zooplankton Data',
    '浮游动物名录': 'Zooplankton Directory',
    '浮游动物数量': 'Zooplankton Quantity',
    '浮游动物门': 'Zooplankton Phylum',
    '浮游动物纲': 'Zooplankton Class',
    '浮游动物目': 'Zooplankton Order',
    '浮游动物科': 'Zooplankton Family',
    '浮游动物属': 'Zooplankton Genus',
    '浮游动物种': 'Zooplankton Type',
    '浮游动物类': 'Zooplankton Kind',
    '浮游动物样品采样表': 'Zooplankton Sample',
    '浮游动物测试数据表': 'Zooplankton TestData',
    '浮游动物样品统计表': 'Sample Statistics',
    '浮游动物详情表': 'Zooplankton Details',
    '蓝藻数据管理': 'Blue Algae Data',
    '无人机图片管理': 'Picture Management',
    '图片标注预览': 'Image Preview',
    '地址管理': 'Address Management',
    '浮游植物': 'Phytoplankton',
    '浮游植物门': 'Phytoplankton Phylum',
    '浮游植物纲': 'Phytoplankton Class',
    '浮游植物目': 'Phytoplankton Order',
    '浮游植物科': 'Phytoplankton Family',
    '浮游植物属': 'Phytoplankton Genus',
    '浮游植物详情表': 'Phytoplankton Particulars',
    '浮游植物数据表': 'Phytoplankton Data',
    '浮游植物样品采样表': 'Sample Parameters',
    '浮游植物样品统计表': 'Sampling statistics',
    '浮游植物测试数据表': 'Phytoplankton Test',
    '浮游植物物种分类表': 'Phytoplankton Type',
    '水质信息': 'Waterquality Information',
    '水质化学指标信息': 'Chemical Waterquality',
    '水质物理指标信息': 'Physical Waterquality',
    '监测站': 'Monitoring Station',
    '监测站基本信息表': 'Monitoring Information',
    '权限管理': 'Authority Management',
    '用户管理': 'User Management',
    '部门管理': 'Department Management',
    '角色管理': 'Role Management',
    '系统设置': 'System Settings',
    '菜单管理': 'Menu Management',
    '参数管理': 'Parameter Management',
    '字典管理': 'Dictionary Management',
    '定时任务': 'Scheduled Tasks',
    '文件上传': 'File Upload',
    '日志管理': 'Log Management',
    '登录日志': 'Login Logs',
    '操作日志': 'Operation Logs',
    '异常日志': 'Error Logs',
};

// 无人机图片管理 (Unmanned Aerial Vehicle Image Management)
t.bga = {}
t.bga.monitor0 = "No"
t.bga.monitor1 = "Yes"
t.bga.fileName = 'File Name';
t.bga.address = 'Address';
t.bga.selectDateTime = 'Select Date and Time';
t.bga.fileId = 'File ID';
t.bga.originalImage = 'Original Image';
t.bga.segmentedResultImage = 'Segmented Result Image';
t.bga.segmentationRatio = 'Segmentation Ratio';
t.bga.createDate = 'Creation Date';
t.bga.remark = 'Remark';
t.bga.view = 'View';
t.bga.annotate = 'Annotate';
t.bga.loading = 'Loading...';
t.bga.urlAddress = 'URL Address';
t.bga.segmentedResultData = 'Segmented Result Data';
t.bga.details = 'Details';
t.bga.Loading = 'Loading...';
t.bga.modelSelect = 'Model Selection';
t.bga.comparePreview = 'Preview';
t.bga.latitude = 'Latitude';
t.bga.longitude = 'Longitude';
t.bga.updateDate = 'Update Date';
t.bga.deleteDate = 'Delete Date'
t.bga.creator = 'Creator';
t.bga.updator = 'Updater';
//home
t.bga.monitoringArea = 'Monitoring Area';
t.bga.dataTotal = 'Total Data';
t.bga.todayUpload = 'Today\'s Uploaded Data';
t.bga.todayTagged = 'Today\'s Tagged Data';

t.phytoplankto = {}
t.phytoplankto.chineseName = 'Chinese Name'
t.phytoplankto.latinName = 'Latin Name'
t.phytoplankto.englishName = 'English Name'
t.phytoplankto.phytoplanktotonDataId = 'Phytoplankton Data ID'
t.phytoplankto.sampleDataInfo= 'Phytoplankton Sample Info'
t.phytoplankto.sampleDataId = 'Sample Data ID'
t.phytoplankto.samplingLocation = 'Sampling Location'
t.phytoplankto.category = 'Category'
t.phytoplankto.species = 'Species'
t.phytoplankto.wetWeight = 'Wet Weight (10^-4 mg)'
t.phytoplankto.count = 'Count'
t.phytoplankto.cellCount = 'Cell Count (cells/L)'
t.phytoplankto.biomass = 'Biomass (mg/L)'
t.phytoplankto.samplingId = 'Sampling ID'
t.phytoplankto.sampleVolumeLiters = 'Sample Volume (L)'
t.phytoplankto.concentratedVolume = 'Concentrated Volume (mL)'
t.phytoplankto.dilutionFactor = 'Dilution Factor'
t.phytoplankto.fieldOfViewCount = 'Field of View Count'
t.phytoplankto.fieldArea = 'Field Area (mm²)'
t.phytoplankto.countingArea = 'Counting Area (mm²)'
t.phytoplankto.countingFrameVolume = 'Counting Frame Volume (mL)'
t.phytoplankto.countingFrameArea = 'Counting Frame Area (mm²)'
t.phytoplankto.samplingAt = 'Sampling Time'
t.phytoplankto.name = 'Phytoplankton Name'
t.phytoplankto.phylumName = 'Phylum'
t.phytoplankto.className = 'Class'
t.phytoplankto.nameOrder = 'Order'
t.phytoplankto.nameFamily = 'Family'
t.phytoplankto.nameGenus = 'Genus'
t.phytoplankto.type = 'Type'
t.phytoplankto.identification = 'Identification'
t.phytoplankto.stationId = 'Monitoring Station'
t.phytoplankto.stationAddr = 'Monitoring Station Address'
t.phytoplankto.autecology = 'Autecology'
t.phytoplankto.contributor = 'Contributor'
t.phytoplankto.reviewer = 'Reviewer'
t.phytoplankto.status = 'Data Status' // 0: Initial submission for review, 1: Approved, 2: Rejected
t.phytoplankto.images = 'Images'
t.phytoplankto.vedios = 'Videos'
t.phytoplankto.attachment = 'Attachments'
t.phytoplankto.description = 'Description'
t.phytoplankto.uploadDataTable = 'Upload Table'
t.phytoplankto.pleaseDownloadTemplate = 'Please Download Template'
t.phytoplankto.downloadTemplate = 'Download Template'
t.phytoplankto.uploadSuccess = 'Upload Success'
t.phytoplankto.uploadError = 'Upload Error'

t.monitoring = {}
t.monitoring.stcd = 'Monitoring Station Code';
t.monitoring.stnm = 'Monitoring Station Name';
t.monitoring.stlvl = 'Monitoring Station Level';
t.monitoring.bnnm = 'Main Stream Position Name';
t.monitoring.rvnm = 'Branch Stream Name';
t.monitoring.eslo = 'Longitude';
t.monitoring.ntla = 'Latitude';
t.monitoring.staddr = 'Monitoring Station Address';
t.monitoring.adcd = 'Administrative Division Code';
t.monitoring.wudcd = 'Surface Water Function Zone Code';
t.monitoring.munit = 'Management Unit';
t.monitoring.msunit = 'Monitoring Unit';
t.monitoring.mnfrq = 'Monitoring Frequency';
t.monitoring.atst = 'Automatic Monitoring'; // 1: yes, 0: no
t.monitoring.fndym = 'Establishment Date'; // Date data type
t.monitoring.endym = 'Withdrawal Date'; // Date data type
t.monitoring.remarks = 'Remarks'; // Used to describe station information or save hyperlinks

t.benthos = {}
t.benthos.zoobtId = 'Benthos ID'
t.benthos.nameCn = 'Benthos Chinese Name'
t.benthos.nameLatin = 'Benthos Latin Name'
t.benthos.creator = 'Benthos Creator'
t.benthos.updater = 'Benthos Updater'
t.benthos.zoobtName = 'Benthos Name'
t.benthos.stationCode = 'Station Code (Site ID)'
t.benthos.stationName = 'Station Name (Site Name)'
t.benthos.averageBiomass = 'Average Biomass'
t.benthos.averageDensity = 'Average Density'
t.benthos.biomass = 'Biomass (g/m2)'
t.benthos.latitudeAndLongitude = 'Latitude and Longitude'
t.benthos.samplingTime = 'Sampling Time'
t.benthos.samplingLocation = 'Sampling Location (Lake/ Watershed)'
t.benthos.samplingTool = 'Sampling Tool'
t.benthos.zoobtSize = 'Benthos Size (cm)'
t.benthos.zoobtPhylumName = 'Benthos Phylum Name'
t.benthos.zoobtPhylumLatinName = 'Benthos Phylum - Latin Name'
t.benthos.zoobtClassName = 'Benthos Class Name'
t.benthos.zoobtClassLatinName = 'Benthos Class - Latin Name'
t.benthos.zoobtOrderName = 'Benthos Order Name'
t.benthos.zoobtOrderLatinName = 'Benthos Order - Latin Name'
t.benthos.zoobtFamilyName = 'Benthos Family Name'
t.benthos.zoobtFamilyLatinName = 'Benthos Family - Latin Name'
t.benthos.zoobtGenusName = 'Benthos Genus Name'
t.benthos.zoobtGenusLatinName = 'Benthos Genus - Latin Name'
t.benthos.zoobtSpeciesName = 'Benthos Species Name'
t.benthos.zoobtSpeciesLatinName = 'Benthos Species - Latin Name'
t.benthos.density = 'Density (ind./m2)'
t.benthos.imageData = 'Image Data (Stored URL)'
t.benthos.videoData = 'Video Data (Stored URL)'
t.benthos.createdAt = 'Created At'
t.benthos.updateAt = 'Updated At'
t.benthos.deleteAt = 'Deleted At'
t.benthos.remark = 'Remark'
t.benthos.sampleDataId = 'Sample Data ID'
t.benthos.samplingLocation = 'Sampling Location'
t.benthos.category = 'Category'
t.benthos.species = 'Species'
t.benthos.wetWeight = 'Wet Weight (10^-4 mg)'
t.benthos.count = 'Count'
t.benthos.cellCount = 'Cell Count (cells/L)'
t.benthos.biomass = 'Biomass (mg/L)'
t.benthos.samplingId = 'Sampling ID'
t.benthos.sampleVolumeLiters = 'Sample Volume (L)'
t.benthos.concentratedVolume = 'Concentrated Volume (mL)'
t.benthos.dilutionFactor = 'Dilution Factor'
t.benthos.fieldOfViewCount = 'Field of View Count'
t.benthos.fieldArea = 'Field Area (mm²)'
t.benthos.countingArea = 'Counting Area (mm²)'
t.benthos.countingFrameVolume = 'Counting Frame Volume (mL)'
t.benthos.countingFrameArea = 'Counting Frame Area (mm²)'
t.benthos.samplingAt = 'Sampling Time'
t.benthos.latinName = 'LatinName'
t.benthos.uploadDataTable = 'Upload Table'
t.benthos.pleaseDownloadTemplate = 'Please Download Template'
t.benthos.downloadTemplate = 'Download Template'
t.benthos.uploadSuccess = 'Upload Success'
t.benthos.uploadError = 'Upload Error'

t.hydrological = {}
t.hydrological.stationCode = 'Station Code'
t.hydrological.waterTemperature = 'Water Temperature (℃)'
t.hydrological.waterLevel = 'Water Level (m)'
t.hydrological.reservoirCapacity = 'Reservoir Capacity'
t.hydrological.inflowRate = 'Inflow Rate (m3/s)'
t.hydrological.outflowRate = 'Outflow Rate (m3/s)'
t.hydrological.waterDepth = 'Water Depth'
t.hydrological.uswl = 'Upstream Water Level (m)'
t.hydrological.dswl = 'Downstream Water Level (m)'
t.hydrological.flowVelocity = 'Flow Velocity (m/s)'
t.hydrological.flowRate = 'Flow Rate (m3/s)'
t.hydrological.sedimentConcentration = 'Sediment Concentration (kg/m3)'
t.hydrological.sedimentTransport = 'Sediment Transport (t/m3/d)'
t.hydrological.flowMeasMethod = 'Flow Measurement Method'
t.hydrological.remark = 'Remark'
t.hydrological.createdAt = 'Created At'


t.waterquality = {}
t.waterquality.stationCode = 'Station Code';
t.waterquality.verticalLineNum = 'Vertical Line Number';
t.waterquality.layerNum = 'Layer Number';
t.waterquality.totalHardness = 'Total Hardness (mg/L)';
t.waterquality.totalAlkalinity = 'Total Alkalinity';
t.waterquality.totalOrganicCarbon = 'Total Organic Carbon (mg/L)';
t.waterquality.dissolvedOrganicCarbon = 'Dissolved Organic Carbon (mg/L)';
t.waterquality.chemicalOxygenDemand = 'Chemical Oxygen Demand (mg/L)';
t.waterquality.totalPhosphorus = 'Total Phosphorus (mg/L)';
t.waterquality.solublePhosphate = 'Soluble Phosphate (mg/L)';
t.waterquality.totalNitrogen = 'Total Nitrogen (mg/L)';
t.waterquality.ammoniaNitrogen = 'Ammonia Nitrogen (mg/L)';
t.waterquality.nitrite = 'Nitrite (mg/L)';
t.waterquality.nitrate = 'Nitrate (mg/L)';
t.waterquality.permanganateIndex = 'Permanganate Index (mg/L)';
t.waterquality.bod5 = '5-Day Biochemical Oxygen Demand (mg/L)';
t.waterquality.copper = 'Copper (mg/L)';
t.waterquality.zinc = 'Zinc (mg/L)';
t.waterquality.arsenic = 'Arsenic (mg/L)';
t.waterquality.mercury = 'Mercury (mg/L)';
t.waterquality.cadmium = 'Cadmium (mg/L)';
t.waterquality.hexavalentChromium = 'Hexavalent Chromium (mg/L)';
t.waterquality.lead = 'Lead (mg/L)';
t.waterquality.iron = 'Iron (mg/L)';
t.waterquality.manganese = 'Manganese (mg/L)';
t.waterquality.nickel = 'Nickel (mg/L)';
t.waterquality.selenium = 'Selenium (mg/L)';
t.waterquality.fluoride = 'Fluoride (mg/L)';
t.waterquality.totalCyanide = 'Total Cyanide (mg/L)';
t.waterquality.sulfide = 'Sulfide (mg/L)';
t.waterquality.chloride = 'Chloride (mg/L)';
t.waterquality.petroleum = 'Petroleum (mg/L)';
t.waterquality.sulfate = 'Sulfate (mg/L)';
t.waterquality.anionicSurfactant = 'Anionic Surfactant (mg/L)';
t.waterquality.pcbs = 'Polychlorinated Biphenyls (mg/L)';
t.waterquality.pahs = 'Polycyclic Aromatic Hydrocarbons (mg/L)';
t.waterquality.volatilePhenols = 'Volatile Phenols (mg/L)';
t.waterquality.fecalColiforms = 'Fecal Coliforms (ind./L)';
t.waterquality.microcystinToxin = 'Microcystin Toxin (μg/L)';
t.waterquality.chlorophyllA = 'Chlorophyll-a (mg/L)';
t.waterquality.waterTemperature = 'Water Temperature (℃)';
t.waterquality.ph = 'pH Value';
t.waterquality.electricalConductivity = 'Electrical Conductivity (μs/cm)';
t.waterquality.turbidity = 'Turbidity (NTU)';
t.waterquality.dissolvedOxygen = 'Dissolved Oxygen (mg/L)';
t.waterquality.transparency = 'Transparency (m)';
t.waterquality.orp = 'Oxidation-Reduction Potential (V)';
t.waterquality.underwaterIllumination = 'Underwater Illumination (mol)';
t.waterquality.suspendedSolidsConcentration = 'Suspended Solids Concentration (mg/L)';
t.waterquality.createdAt = 'Created At';
t.waterquality.remark = 'Remark';

t.zooplankton = {}
t.zooplankton.zptId = 'Zooplankton ID';
t.zooplankton.nameCn = 'Zooplankton Chinese Name'
t.zooplankton.nameLatin = 'Zooplankton Latin Name'
t.zooplankton.creator = 'Zooplankton Creator'
t.zooplankton.updater = 'Zooplankton Updater'
t.zooplankton.zptName = 'Zooplankton Name';
t.zooplankton.stationCode = 'Station Code';
t.zooplankton.stationName = 'Station Name';
t.zooplankton.biomass = 'Biomass (g/m2)';
t.zooplankton.zptCategoryName = 'Zooplankton Category Name';
t.zooplankton.zptCategoryLatinName = 'Zooplankton Category Latin Name';
t.zooplankton.zptGenusName = 'Zooplankton Genus Name';
t.zooplankton.zptGenusLatinName = 'Zooplankton Genus Latin Name';
t.zooplankton.zptSpeciesName = 'Zooplankton Species Name';
t.zooplankton.zptSpeciesLatinName = 'Zooplankton Species Latin Name';
t.zooplankton.zptCount = 'Zooplankton Count';
t.zooplankton.samplingVolume = 'Sampling Volume (L)';
t.zooplankton.individualDensity = 'Individual Density (ind/L)';
t.zooplankton.individualFreshWeight = 'Individual Fresh Weight (mg)';
t.zooplankton.remoteSensingData = 'Remote Sensing Data (URL)';
t.zooplankton.imageData = 'Image Data (URL)';
t.zooplankton.videoData = 'Video Data (URL)';
t.zooplankton.createdAt = 'Created At';
t.zooplankton.updateAt = 'Updated At';
t.zooplankton.deleteAt = 'Deleted At';
t.zooplankton.remark = 'Remark';
t.zooplankton.sampleDataId = 'Sample Data ID'
t.zooplankton.samplingLocation = 'Sampling Location'
t.zooplankton.category = 'Category'
t.zooplankton.species = 'Species'
t.zooplankton.wetWeight = 'Wet Weight (10^-4 mg)'
t.zooplankton.count = 'Count'
t.zooplankton.cellCount = 'Cell Count (cells/L)'
t.zooplankton.biomass = 'Biomass (mg/L)'
t.zooplankton.samplingId = 'Sampling ID'
t.zooplankton.sampleDataInfo = 'Sampling Data Info'
t.zooplankton.sampleVolumeLiters = 'Sample Volume (L)'
t.zooplankton.concentratedVolume = 'Concentrated Volume (mL)'
t.zooplankton.dilutionFactor = 'Dilution Factor'
t.zooplankton.fieldOfViewCount = 'Field of View Count'
t.zooplankton.fieldArea = 'Field Area (mm²)'
t.zooplankton.countingArea = 'Counting Area (mm²)'
t.zooplankton.countingFrameVolume = 'Counting Frame Volume (mL)'
t.zooplankton.countingFrameArea = 'Counting Frame Area (mm²)'
t.zooplankton.samplingAt = 'Sampling Time'
t.zooplankton.latinName = 'LatinName'
t.zooplankton.uploadDataTable = 'Upload Table'
t.zooplankton.pleaseDownloadTemplate = 'Please Download Template'
t.zooplankton.downloadTemplate = 'Download Template'
t.zooplankton.uploadSuccess = 'Upload Success'
t.zooplankton.uploadError = 'Upload Error'


t.weather = {}
t.weather.stationCode = 'Station Code';
t.weather.windSpeed = 'Wind Speed (m/s)';
t.weather.windDirection = 'Wind Direction (°)';
t.weather.temperature = 'Temperature (℃)';
t.weather.atmosphericPressure = 'Atmospheric Pressure (Pa)';
t.weather.rainfall = 'Rainfall (mm)';
t.weather.par = 'Photosynthetically Active Radiation (μmol/m2/s)';
t.weather.relativeHumidity = 'Relative Humidity (%)';
t.weather.solarRadiation = 'Solar Radiation (W/m2)';
t.weather.createdAt = 'Created At';
t.weather.remark = 'Remark';



t.schedule = {}
t.schedule.beanName = 'Bean Name'
t.schedule.beanNameTips = 'Spring bean name, e.g., testTask'
t.schedule.pauseBatch = 'Pause'
t.schedule.resumeBatch = 'Resume'
t.schedule.runBatch = 'Execute'
t.schedule.log = 'Log List'
t.schedule.params = 'Parameters'
t.schedule.cronExpression = 'Cron Expression'
t.schedule.cronExpressionTips = 'e.g., 0 0 12 * * ?'
t.schedule.remark = 'Remark'
t.schedule.status = 'Status'
t.schedule.status0 = 'Paused'
t.schedule.status1 = 'Normal'
t.schedule.statusLog0 = 'Failed'
t.schedule.statusLog1 = 'Succeeded'
t.schedule.pause = 'Pause'
t.schedule.resume = 'Resume'
t.schedule.run = 'Execute'
t.schedule.jobId = 'Task ID'
t.schedule.times = 'Time Consumed (in milliseconds)'
t.schedule.createDate = 'Execution Time'

t.oss = {}
t.oss.config = 'Cloud Storage Configuration'
t.oss.upload = 'Upload File'
t.oss.url = 'URL Address'
t.oss.urlRes = 'Result URL Address'
t.oss.createDate = 'Creation Date'
t.oss.type = 'Type'
t.oss.type1 = 'Qiniu'
t.oss.type2 = 'Aliyun'
t.oss.type3 = 'Tencent Cloud'
t.oss.qiniuDomain = 'Domain'
t.oss.qiniuDomainTips = 'Qiniu-bound domain'
t.oss.qiniuPrefix = 'Path Prefix'
t.oss.qiniuPrefixTips = 'Leave it empty by default'
t.oss.qiniuAccessKey = 'Access Key'
t.oss.qiniuAccessKeyTips = 'Qiniu Access Key'
t.oss.qiniuSecretKey = 'Secret Key'
t.oss.qiniuSecretKeyTips = 'Qiniu Secret Key'
t.oss.qiniuBucketName = 'Bucket Name'
t.oss.qiniuBucketNameTips = 'Qiniu storage bucket name'
t.oss.aliyunDomain = 'Domain'
t.oss.aliyunDomainTips = 'Aliyun-bound domain'
t.oss.aliyunPrefix = 'Path Prefix'
t.oss.aliyunPrefixTips = 'Leave it empty by default'
t.oss.aliyunEndPoint = 'End Point'
t.oss.aliyunEndPointTips = 'Aliyun End Point'
t.oss.aliyunAccessKeyId = 'Access Key ID'
t.oss.aliyunAccessKeyIdTips = 'Aliyun Access Key ID'
t.oss.aliyunAccessKeySecret = 'Access Key Secret'
t.oss.aliyunAccessKeySecretTips = 'Aliyun Access Key Secret'
t.oss.aliyunBucketName = 'Bucket Name'
t.oss.aliyunBucketNameTips = 'Aliyun Bucket Name'
t.oss.qcloudDomain = 'Domain'
t.oss.qcloudDomainTips = 'Tencent Cloud-bound domain'
t.oss.qcloudPrefix = 'Path Prefix'
t.oss.qcloudPrefixTips = 'Leave it empty by default'
t.oss.qcloudAppId = 'App ID'
t.oss.qcloudAppIdTips = 'Tencent Cloud App ID'
t.oss.qcloudSecretId = 'Secret ID'
t.oss.qcloudSecretIdTips = 'Tencent Cloud Secret ID'
t.oss.qcloudSecretKey = 'Secret Key'
t.oss.qcloudSecretKeyTips = 'Tencent Cloud Secret Key'
t.oss.qcloudBucketName = 'Bucket Name'
t.oss.qcloudBucketNameTips = 'Tencent Cloud Bucket Name'
t.oss.qcloudRegion = 'Region'
t.oss.qcloudRegionTips = 'Select'
t.oss.qcloudRegionBeijing1 = 'Beijing (North China)'
t.oss.qcloudRegionBeijing = 'Beijing'
t.oss.qcloudRegionShanghai = 'Shanghai (East China)'
t.oss.qcloudRegionGuangzhou = 'Guangzhou (South China)'
t.oss.qcloudRegionChengdu = 'Chengdu (Southwest)'
t.oss.qcloudRegionChongqing = 'Chongqing'
t.oss.qcloudRegionSingapore = 'Singapore'
t.oss.qcloudRegionHongkong = 'Hong Kong'
t.oss.qcloudRegionToronto = 'Toronto'
t.oss.qcloudRegionFrankfurt = 'Frankfurt'

t.dept = {}
t.dept.name = 'Name'
t.dept.parentName = 'Parent Department'
t.dept.sort = 'Sort'
t.dept.parentNameDefault = 'Top-level Department'
t.dept.chooseerror = 'Please choose a department'
t.dept.title = 'Select Department'

t.dict = {}
t.dict.dictName = 'Dictionary Name'
t.dict.dictType = 'Dictionary Type'
t.dict.dictLabel = 'Dictionary Label'
t.dict.dictValue = 'Dictionary Value'
t.dict.dictSys = 'Dictionary Manage'
t.dict.sort = 'Sort'
t.dict.remark = 'Remark'
t.dict.createDate = 'Creation Date'

t.logError = {}
t.logError.requestUri = 'Request URI'
t.logError.requestMethod = 'Request Method'
t.logError.requestParams = 'Request Parameters'
t.logError.ip = 'Operation IP'
t.logError.userAgent = 'User Agent'
t.logError.createDate = 'Creation Date'
t.logError.errorInfo = 'Error Information'

t.logLogin = {}
t.logLogin.creatorName = 'Username'
t.logLogin.status = 'Status'
t.logLogin.status0 = 'Failed'
t.logLogin.status1 = 'Succeeded'
t.logLogin.status2 = 'Account Locked'
t.logLogin.operation = 'Operation Type'
t.logLogin.operation0 = 'Login'
t.logLogin.operation1 = 'Logout'
t.logLogin.ip = 'Operation IP'
t.logLogin.userAgent = 'User-Agent'
t.logLogin.createDate = 'Creation Date'

t.logOperation = {}
t.logOperation.status = 'Status'
t.logOperation.status0 = 'Failed'
t.logOperation.status1 = 'Succeeded'
t.logOperation.creatorName = 'Username'
t.logOperation.operation = 'User Operation'
t.logOperation.requestUri = 'Request URI'
t.logOperation.requestMethod = 'Request Method'
t.logOperation.requestParams = 'Request Parameters'
t.logOperation.requestTime = 'Request Duration'
t.logOperation.ip = 'Operation IP'
t.logOperation.userAgent = 'User-Agent'
t.logOperation.createDate = 'Creation Date'

t.menu = {}
t.menu.name = 'Name'
t.menu.icon = 'Icon'
t.menu.type = 'Type'
t.menu.type0 = 'Menu'
t.menu.type1 = 'Button'
t.menu.sort = 'Sort'
t.menu.url = 'Route'
t.menu.openStyle = 'Open Style'
t.menu.openStyle0 = 'External open'
t.menu.openStyle1 = 'Internal open'
t.menu.permissions = 'Authorization Identifier'
t.menu.permissionsTips = 'Multiple identifiers separated by commas, e.g., sys:menu:save,sys:menu:update'
t.menu.parentName = 'Parent Menu'
t.menu.parentNameDefault = 'Top-level Menu'
t.menu.resource = 'Authorization Resource'
t.menu.resourceUrl = 'Resource URL'
t.menu.resourceMethod = 'Request Method'
t.menu.resourceAddItem = 'Add an item'

t.params = {}
t.params.paramCode = 'Code'
t.params.paramValue = 'Value'
t.params.remark = 'Remark'

t.role = {}
t.role.name = 'Name'
t.role.remark = 'Remark'
t.role.createDate = 'Creation Date'
t.role.menuList = 'Menu Authorization'
t.role.deptList = 'Data Authorization'

t.user = {}
t.user.username = 'Username'
t.user.deptName = 'Department'
t.user.email = 'Email'
t.user.mobile = 'Mobile Number'
t.user.status = 'Status'
t.user.status0 = 'Disabled'
t.user.status1 = 'Normal'
t.user.createDate = 'Creation Date'
t.user.password = 'Password'
t.user.confirmPassword = 'Confirm Password'
t.user.realName = 'Real Name'
t.user.gender = 'Gender'
t.user.gender0 = 'Male'
t.user.gender1 = 'Female'
t.user.gender2 = 'Secret'
t.user.roleIdList = 'Role Configuration'
t.user.validate = {}
t.user.validate.confirmPassword = 'Confirmation password does not match the password input'
t.user.select = 'Select User'
t.user.selecterror = 'Please select one record'

export default t
