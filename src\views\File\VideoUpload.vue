<template>
  <div class="mod-bga__videoupload">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="state.deleteHandle()">删除</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="handleFileUpload">{{ $t("上传视频") }}</el-button>
      </el-form-item>
<!--      <el-form-item>-->
<!--        <el-button type="success" @click="handleFileDownLoad">{{ $t("下载视频") }}</el-button>-->
<!--      </el-form-item>-->
      <el-form-item>
      </el-form-item>
      <el-upload action="http://39.129.29.230:30838/api/v1/minio-upload" class="upload-demo" name="file" :limit="100" ref="upload" :auto-upload="false" :data="{ metaid: route.meta.routerId }" drag @change="handleFileChange">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将视频拖到此处，或<em>点击上传</em></div>
      </el-upload>
      <el-form-item>
        <input type="file" webkitdirectory @change="handleFolderSelect" ref="folderInput" accept="image/jpeg,image/jpg" />
        <el-button type="success" @click="handleReadFolderImageUpload">{{ $t("读取文件夹中视频数据并上传") }}</el-button>
      </el-form-item>
<!--      <template>-->
<!--        <div class="video-container">-->
<!--          <video ref="videoPlayer" controls playsinline preload="metadata" :poster="thumbnailUrl">-->
<!--            <source src="http://39.129.29.230:30838/data-bga/data-bga-common/0950d63970dance_girl.mp4" type="video/mp4">-->
<!--            <p>您的浏览器不支持HTML5视频，请<a :href="videoUrl" download>下载视频</a></p>-->
<!--          </video>-->
<!--        </div>-->
<!--      </template>-->
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="stationCode" label="站点编码" header-align="center" align="center"></el-table-column>
      <el-table-column prop="stationName" label="站点名称" header-align="center" align="center"></el-table-column>
      <el-table-column prop="dataType" label="数据类型(图片、视频、音频)" header-align="center" align="center"></el-table-column>
      <el-table-column prop="description" label="描述" header-align="center" align="center"></el-table-column>
      <el-table-column prop="dataSource" label="数据来源" header-align="center" align="center"></el-table-column>
      <el-table-column prop="videoAddress" label="视频地址minio" header-align="center" align="center"></el-table-column>
      <el-table-column prop="labelAddress" label="标注地址" header-align="center" align="center"></el-table-column>
      <el-table-column prop="time" label="日期" header-align="center" align="center"></el-table-column>
      <el-table-column label="预览" header-align="center" align="center" width="120">
        <template v-slot="scope">
          <el-button
            type="primary"
            link
            @click="previewVideo(scope.row.videoAddress)"
          >
            预览
          </el-button>
        </template>
      </el-table-column>
<!--      <el-table-column label="下载" header-align="center" align="center" width="120">-->
<!--        <template v-slot="scope">-->
<!--          <el-button-->
<!--            type="primary"-->
<!--            link-->
<!--            @click="downloadVideo(scope.row.videoAddress)"-->
<!--          >-->
<!--            下载-->
<!--          </el-button>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="操作" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <el-button type="primary" link @click="state.deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">确定</add-or-update>
    <el-dialog
      v-model="videoDialogVisible"
      title="视频预览"
      width="70%"
      top="5vh"
    >
      <div class="video-preview-container">
        <video
          v-if="currentVideoUrl"
          ref="videoPlayer"
          controls
          autoplay
          style="width: 100%; max-height: 70vh;"
        >
          <source :src="currentVideoUrl" type="video/mp4">
          您的浏览器不支持HTML5视频
        </video>
        <div v-else class="no-video">
          <el-empty description="无法加载视频"></el-empty>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./VideoUpload-add-or-update.vue";
import { ElLoading, ElMessage } from "element-plus";
import axios from "axios";
import baseService from "@/service/baseService";
import { useRoute } from "vue-router";
const route = useRoute();
const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/video/videoupload/page",
  getDataListIsPage: true,
  exportURL: "/video/videoupload/export",
  deleteURL: "/video/videoupload"
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};

const state_excel = reactive({
  selectedFile: null as File | null,
  selectedFiles: [] as Array<{ raw: File; name: string; status: string }>, // 新增文件夹文件列表
  isUploading: false, // 新增上传状态
  folderName: "" // 新增字段存储文件夹名称
});

//文件上传
function handleFileChange(file: any, fileList: any) {
  // 当用户选择文件时，更新selectedFile
  if (fileList.length > 0) {
    state_excel.selectedFile = fileList[0].raw; // 使用 fileList[0].raw 获取原始 File 对象
  } else {
    state_excel.selectedFile = null; // 如果没有文件，则设置为 null
  }
}

function handleFileUpload() {
  //判断state_excel.selectedFile是否有值
  if (!state_excel.selectedFile) {
    ElMessage.error("请选择文件");
    return;
  }
  const formData = new FormData();
  formData.append("file", state_excel.selectedFile);
  // 添加额外元数据
  formData.append("Content-Type", "application/octet-stream");
  formData.append("originalFilename", state_excel.selectedFile.name);
  baseService
    .post("files/upload", formData, {
      params: {
        filename: state_excel.selectedFile.name,
        type: "single" // 标识单文件上传
      }
    })
    .then((response) => {
      console.log("测试response.data:", response.data);
      const FilePath = response.data.publicUrl;
      const url = new URL(FilePath);
      const objectName = url.pathname.substring(1);
      console.log("测试FilePath:", FilePath);
      baseService.post("/video/videoupload/saveVideoUploadPath", { filePath: objectName });
      console.log("上传成功");
      ElMessage.success("上传成功");
      ElMessage.success("视频地址已存储");
    })
    .catch((error) => {
      console.error("错误:", error);
      if (error instanceof Error) {
        ElMessage.error(`上传失败: ${error.message}`);
      } else {
        ElMessage.error("上传失败");
      }
    });
}
//读取文件夹数据批量上传测试
// 触发文件夹选择
const triggerFolderSelect = () => {
  (document.querySelector(".folder-input") as HTMLInputElement)?.click();
};

// 处理文件夹选择
const handleFolderSelect = async (e: Event) => {
  const input = e.target as HTMLInputElement;
  const files = Array.from(input.files || []);

  if (files.length > 0) {
    // 从第一个文件的路径中提取文件夹名称
    const firstFile = files[0];
    const fullPath = (firstFile as any).webkitRelativePath; // 获取完整路径
    const folderName = fullPath.split("/")[0]; // 提取第一层目录名
    state_excel.folderName = folderName; // 保存文件夹名称

    state_excel.selectedFiles = files
      .filter((f) => f.name.match(/\.(mp4)$/i))
      .map((file) => ({
        raw: file,
        name: file.name,
        status: "ready"
      }));
  }
};
const handleReadFolderImageUpload = async () => {
  if (state_excel.selectedFiles.length === 0) {
    ElMessage.error("请选择包含视频的文件夹");
    return;
  }

  if (!state_excel.folderName) {
    // 检查文件夹名称是否存在
    ElMessage.error("无法获取文件夹名称");
    return;
  }

  state_excel.isUploading = true;
  openLoading();
  try {
    const results = await Promise.all(
      state_excel.selectedFiles.map(async (file) => {
        try {
          file.status = "uploading";

          const formData = new FormData();
          formData.append("file", file.raw);
          formData.append("Content-Type", "application/octet-stream");

          const res = await baseService.post("files/upload", formData, { params: { path: state_excel.folderName }, headers: { "Content-Type": "multipart/form-data" } });
          const FilePath = res.data.publicUrl;
          const url = new URL(FilePath);
          const objectName = url.pathname.substring(1);

          await baseService.post("/video/videoupload/saveVideoUploadPath", {
            filePath: objectName
          });

          file.status = "success";
          return true;
        } catch (e) {
          file.status = "error";
          throw e;
        }
      })
    );

    ElMessage.success(`成功上传${results.length}个文件`);
  } catch (error) {
    console.error("批量上传失败:", error);
    if (error instanceof Error) {
      ElMessage.error(`上传失败: ${error.message}`);
    } else {
      ElMessage.error("上传失败");
    }
  } finally {
    closeLoading();
    state_excel.isUploading = false;
  }
};
const openLoading = () => {
  ElLoading.service({
    lock: true,
    text: "上传中",
    background: "rgba(0, 0, 0, 0.7)"
  });
};
const closeLoading = () => {
  ElLoading.service().close();
};

const videoDialogVisible = ref(false);
const currentVideoUrl = ref("");
const videoLoading = ref(false);

const downloadVideo = async (objectName: string) => {
  if (!objectName) {
    ElMessage.warning("视频地址无效");
    return;
  }
  //console.log("测试objectName:", objectName);
  const fileName = objectName.split('/').pop() || objectName;
  //console.log("测试fileName:", fileName);
  try {
    const response = await baseService.get("/files/download", {
      objectName: fileName
    });

    ElMessage.success("下载已开始");
  } catch (error: any) {
    console.error("下载失败:", error);
    ElMessage.error("下载失败: " + error.message);
  }
};

// 添加预览方法
const previewVideo = async (objectName: string) => {
  if (!objectName) {
    ElMessage.warning("视频地址无效");
    return;
  }

  videoLoading.value = true;
  videoDialogVisible.value = true;
  currentVideoUrl.value = "";

  // 提取纯文件名
  const fileName = objectName.replace(/^data-bga\//, '');
  try {
    const response = await baseService.post("/files/video-preview-url", {
      objectName: fileName,
      usePublicUrl: true // 根据后端API需要调整
    });

    if (!response.data) {
      throw new Error("无效的视频URL");
    }

    // 假设后端返回的是直接URL或包含URL的对象
    const videoUrl = response.data.data || response.data;

    if (typeof videoUrl !== 'string' || !videoUrl.startsWith('http')) {
      throw new Error("视频URL格式不正确");
    }

    currentVideoUrl.value = videoUrl;
  } catch (error: any) {
    console.error("获取视频预览失败:", error);
    ElMessage.error("获取视频预览失败: " + error.message);
    currentVideoUrl.value = "";
    if (error instanceof Error) {
      ElMessage.error(`上传失败: ${error.message}`);
    } else {
      ElMessage.error("上传失败");
    }
  } finally {
    videoLoading.value = false;
  }
};
</script>
