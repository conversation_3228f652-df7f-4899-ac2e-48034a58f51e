<template>
  <div class="mod-bga__phytosampledata">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
<!--      <el-form-item>-->
<!--        <el-button type="primary" @click="uploadBgaDataHandle()">{{ $t("benthos.uploadDataTable") }}</el-button>-->
<!--      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" @click="downloadFile">下载模板</el-button>
      </el-form-item>
      <el-upload action="benthos/zoobtdatanew/excel-input" class="upload-demo" name="file" :limit="1" ref="upload" :auto-upload="false" accept=".xlsx,.xls,.xlsm"
                 :data="{ metaid: route.meta.routerId }" drag @change="handleFileChange" >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将Excel文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip">只能上传.xlsx/.xls/.xlsm文件</div>
      </el-upload>
      <el-button type="success" @click="handleFileUpload">{{ $t("导入Excel数据至数据库") }}</el-button>
    </el-form>
    <div style="display: flex; justify-content: left; align-items: start">
      <el-tree style="max-width: 300px; min-width: 300px" :data="treeData" :props="defaultProps" accordion @node-click="handleNodeClick" />
      <el-main>
        <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()" style="margin-top: 20px">
          <el-form-item>
            <el-form-item>
              <el-input style="width: 200px" v-model="state.dataForm.monitorName" placeholder="监测站点名称" clearable></el-input>
            </el-form-item>
            <!--时间查询-->
            <el-form-item label="开始时间">
              <el-date-picker v-model="state.dataForm.startTime" type="datetime" placeholder="选择开始时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"></el-date-picker>
            </el-form-item>
            <el-form-item label="结束时间">
              <el-date-picker v-model="state.dataForm.endTime" type="datetime" placeholder="选择结束时间" format="YYYY-MM-DD HH:mm:ss" value-format="YYYY-MM-DD HH:mm:ss"></el-date-picker>
            </el-form-item>
            <!--时间查询-->
            <el-form-item>
              <el-button @click="search">{{ $t("query") }}</el-button>
            </el-form-item>
            <el-button type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="info" @click="state.exportHandle()">导出</el-button>
          </el-form-item>
        </el-form>
        <div v-if="state.dataList && state.dataList.length > 0">
          <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
            <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
            <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="samplingId" :label="$t('benthos.samplingId')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="waterbodyName" :label="$t('benthos.waterbodyName')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="monitorName" :label="$t('benthos.monitorName')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="monitorUnit" :label="$t('benthos.monitorUnit')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="timeYear" :label="$t('benthos.timeYear')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="timeMonth" :label="$t('benthos.timeMonth')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="timeDay" :label="$t('benthos.timeDay')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="timeDetail" :label="$t('benthos.timeDetail')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="zoobtPhylum" :label="$t('benthos.zoobtPhylum')" header-align="center" align="center"></el-table-column>
            <el-table-column width="150" prop="zoobtClass" :label="$t('benthos.zoobtClass')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="zoobtOrder" :label="$t('benthos.zoobtOrder')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="zoobtFamily" :label="$t('benthos.zoobtFamily')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="zoobtGenus" :label="$t('benthos.zoobtGenus')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="zoobtName" :label="$t('benthos.zoobtName')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="zoobtLatin" :label="$t('benthos.zoobtLatin')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="density" :label="$t('benthos.density')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="wetWeight" :label="$t('benthos.wetWeight')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="biomass" :label="$t('benthos.biomass')" header-align="center" align="center"></el-table-column>
            <el-table-column width="160" prop="notes" :label="$t('benthos.notes')" header-align="center" align="center"></el-table-column>
            <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
              <template v-slot="scope">
                <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("update") }}</el-button>
                <el-button type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"></el-pagination>
        </div>
      </el-main>
    </div>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
    <update-bga-data :uploadPath="state.uploadURL" ref="updateBgaDataRef" @refreshDataList="state.getDataList"></update-bga-data>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./zoobtdatanew-add-or-update.vue";
import { globalLanguage } from "@/utils/globalLang";
import UpdateBgaData from "@/components/uploadExcel/update-bga-data.vue";
import baseService from "@/service/baseService";
import { useRoute } from "vue-router";
import { ElLoading, ElMessage } from "element-plus"; // 导入 useRoute
const route = useRoute();
const { $t } = globalLanguage();
const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/benthos/zoobtdatanew/page",
  getDataListIsPage: true,
  exportURL: "/benthos/zoobtdatanew/export",
  deleteURL: "/benthos/zoobtdatanew",
  uploadURL: "/benthos/zoobtdatanew/upload",
  dataForm: {
    id: "",
    year: "",
    month: "",
    monitorName: "",
    stationId: "",
    startTime: "", // 开始时间
    endTime: "" // 结束时间
  }
});
interface Tree {
  label: string;
  children?: Tree[];
}
const handleNodeClick = (data: any, getNode: any) => {
  const month = getNode.parent.label;
  const year = month ? getNode.parent.parent.label : undefined;
  if (getNode.data.children.length == 0) {
    console.log("查询", year, month, data.label);
    if (year === undefined && month === undefined) {
      state.dataForm.year = data.label;
      state.getDataList();
      return;
    }
    if (year === undefined) {
      state.dataForm.year = month;
      state.dataForm.month = data.label;
      state.getDataList();
      return;
    }
    state.dataForm.year = year;
    state.dataForm.month = month;
    state.dataForm.monitorName = data.label;
    state.getDataList();
  }
};
const treeData = reactive<Tree[]>([]); // null>([]); // 定义一个响应式变量用于存储树形数据

// 获取树形数据
const getTreeData = () => {
  baseService.get("/benthos/condition_zoobt/list", { limit: 100000 }).then((res) => {
    // 假设 res.data 是服务器返回的数据
    const data = res.data.map((item: any) => ({
      label: item.label,
      children: item.children || [] // 确保 children 是一个数组
    }));
    //先清空treeData数组
    treeData.splice(0, treeData.length);
    // 将获取到的数据赋值给 treeData
    treeData.push(...data); // 使用 push 将元素添加到响应式数组中
  });
};
// 获取月份数据
getTreeData();
const defaultProps = {
  children: "children",
  label: "label"
};

const state = reactive({ ...useView(view), ...toRefs(view) });

const search = () => {
  state.dataForm.year = "";
  state.dataForm.month = "";
  state.getDataList();
};

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
const updateBgaDataRef = ref();
const uploadBgaDataHandle = () => {
  updateBgaDataRef.value.init();
};

const state_excel = reactive<any>({
  selectedFile: null
});

function handleFileChange(file : any, fileList : any) {
  // 当用户选择文件时，更新selectedFile
  if (fileList.length > 0) {
    state_excel.selectedFile = fileList[0].raw; // 使用 fileList[0].raw 获取原始 File 对象
  } else {
    state_excel.selectedFile = null; // 如果没有文件，则设置为 null
  }
}

function handleFileUpload() {
  const formData = new FormData();
  formData.append("file", state_excel.selectedFile);
  openLoading();
  baseService
    .post("/benthos/zoobtdatanew/excel-input", formData)
    .then((response) => {
      console.log("成功:", response.data);
      // 上传成功后，刷新数据列表
      state.getDataList();
      closeLoading();
      // 获取月份数据
      getTreeData();
      // 清空已选择的文件
      state_excel.selectedFile = null;
      //清空已选择的文件列表
      updateBgaDataRef.value.fileList = [];
    })
    .catch((error) => {
      console.error("错误:", error);
      ElMessage.success("上传失败");
      // 上传成功后，刷新数据列表
      state.getDataList();
      closeLoading();
      // 获取月份数据
      getTreeData();
      // 清空已选择的文件
      state_excel.selectedFile = null;
      //清空已选择的文件列表
      updateBgaDataRef.value.fileList = [];
    });

}

const downloadFile = () => {
  const downloadLink = document.createElement("a");
  downloadLink.href = "/ZoobtExcelDataImport.xlsx"; // 文件相对于 public 文件夹的路径
  downloadLink.download = "底栖动物Excel数据表模板.xlsx"; // 下载时的文件名

  // 模拟点击下载链接
  document.body.appendChild(downloadLink);
  downloadLink.click();
  document.body.removeChild(downloadLink);
};

const openLoading = () => {
  ElLoading.service({
    lock: true,
    text: "上传中",
    background: "rgba(0, 0, 0, 0.7)"
  });
};
const closeLoading = () => {
  ElLoading.service().close();
};
</script>

