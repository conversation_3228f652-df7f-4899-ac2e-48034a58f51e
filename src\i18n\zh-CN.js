const t = {};

t.loading = "加载中...";

t.brand = {};
t.brand.lg = "洱海特有生物种质资源智慧管理平台";
t.brand.mini = "生物种质资源智慧管理平台";

t.add = "新增";
t.delete = "删除";
t.deleteBatch = "删除";
t.update = "修改";
t.query = "查询";
t.export = "导出";
t.handle = "操作";
t.confirm = "确定";
t.cancel = "取消";
t.clear = "清除";
t.logout = "退出";
t.manage = "处理";
t.createDate = "创建时间";
t.keyword = "关键字：";
t.choose = "请选择";
t.details = "详情";
t.home = "主页";
t.noData = "暂无数据";
t.uploadFile = "上传";
t.prompt = {};
t.prompt.title = "提示";
t.prompt.info = "确定进行[{handle}]操作?";
t.prompt.success = "操作成功";
t.prompt.failed = "操作失败";
t.prompt.deleteBatch = "请选择删除项";
t.prompt.handleBatch = "请选择操作项";

t.validate = {};
t.validate.required = "必填项不能为空";
t.validate.format = "{attr}格式错误";

t.import = "导入Excel表数据";

t.upload = {};
t.upload.text = "将文件拖到此处，或点击上传";
t.upload.tip = "只支持 jpg / png / jif 格式文件！";
t.upload.button = "点击上传";
t.upload.imgFormat = "请上传jpeg格式的图片";
t.upload.videoFormat = "请上传video格式的视频";
t.upload.size = "大小超过限制!";

t.datePicker = {};
t.datePicker.range = "至";
t.datePicker.start = "开始日期";
t.datePicker.end = "结束日期";

t.fullscreen = {};
t.fullscreen.prompt = "您的浏览器不支持此操作";

t.updatePassword = {};
t.updatePassword.title = "修改密码";
t.updatePassword.loginOut = "退出登录";
t.updatePassword.username = "账号";
t.updatePassword.password = "原密码";
t.updatePassword.newPassword = "新密码";
t.updatePassword.confirmPassword = "确认密码";
t.updatePassword.validate = {};
t.updatePassword.validate.confirmPassword = "确认密码与新密码输入不一致";

t.contentTabs = {
  关闭当前标签页: "关闭当前标签页",
  关闭其它标签页: "关闭其它标签页",
  关闭全部标签页: "关闭全部标签页"
};
t.contentTabs.closeCurrent = "关闭当前标签页";
t.contentTabs.closeOther = "关闭其它标签页";
t.contentTabs.closeAll = "关闭全部标签页";
t.contentTabs.cantClose = "只剩下一个标签页，不支持关闭";

/* 页面 */
t.notFound = {};
t.notFound.desc = "抱歉！您访问的页面<em>失联</em>啦...";
t.notFound.back = "返回";
t.notFound.home = "首页";
t.notFound.title = "访问页面不存在";
t.notFound.error = "访问出错了";

t.login = {};
t.login.title = "登录";
t.login.username = "用户名";
t.login.password = "密码";
t.login.captcha = "验证码";
t.login.demo = "在线演示";
t.login.copyright = "蓝藻水华平台";
t.login.success = "登录成功";

t.theme = {
  themeConfig: "主题设置",
  darkSidebar: "暗色侧边栏",
  lightSidebar: "亮色侧边栏",
  darkTopBar: "暗色顶栏",
  lightTopBar: "亮色顶栏",
  themedTopBar: "主题色顶栏",
  DawnBlue: "拂晓蓝",
  Cyan: "青色",
  Blue: "蓝色",
  Green: "绿色",
  BlueGreen: "蓝绿色",
  Indigo: "靛青色",
  Brown: "棕色",
  Purple: "紫色",
  Gray: "灰色",
  Orange: "橙色",
  MixedRed: "混红色",
  Yellow: "黄色",
  Red: "红色",
  Black: "暗黑色",
  leftMenuLayout: "左侧菜单栏布局",
  topMenuLayout: "顶部菜单栏布局",
  mixedMenuLayout: "混合菜单栏布局",
  contentCover: "内容区域铺满",
  otherConfig: "其他配置",
  fixedLogoBar: "固定Logo栏",
  colorfulSidebarIcons: "侧栏彩色图标",
  exclusiveExpandSidebar: "侧栏排他展开",
  tagDisplayStyle: "标签显示风格",
  tagStyleDefault: "默认",
  tagStyleDot: "原点",
  tagStyleCard: "卡片",
  layoutModes: "布局模式"
};

t.address = {
  晖湾北水域: "晖湾北水域",
  薰衣草庄园: "薰衣草庄园",
  金宝山: "金宝山",
  西华湿地: "西华湿地",
  古滇: "古滇",
  晖北水域: "晖湾北水域",
  通河街附近水域: "通河街附近水域",
  滇池: "滇池",
  四家村水域附近: "四家村水域附近",
  大悲阁: "大悲阁",
  福保路附近水域: "福保路附近水域",
  工人疗养院: "工人疗养院",
  团山: "团山",
  黄金支队: "黄金支队",
  龙门藻站: "龙门藻站",
  大咀头: "大咀头",
  海口红泥咀: "海口红泥咀",
  海口蒋凹村: "海口蒋凹村",
  海月路1号: "海月路1号",
  江尾村: "江尾村",
  晋宁大河尾水域: "晋宁大河尾水域",
  鸽子湾: "鸽子湾",
  海口彩云湾: "海口彩云湾",
  晋宁河嘴水域: "晋宁河嘴水域",
  晋宁镇海阁水域: "晋宁镇海阁水域",
  梁王: "梁王",
  海东湿地: "海东湿地",
  王官湿地: "王官湿地",
  沙堤: "沙堤",
  草海: "草海",
  大湾: "大湾",
  七十郎: "七十郎"
};

// 无人机图片管理
t.bga = {};
t.bga.monitor0 = "否";
t.bga.monitor1 = "是";
t.bga.fileName = "文件名";
t.bga.address = "地址";
t.bga.selectDateTime = "选择日期时间";
t.bga.fileId = "文件Id";
t.bga.originalImage = "原图";
t.bga.segmentedResultImage = "分割后结果图";
t.bga.segmentationRatio = "分割比例";
t.bga.createDate = "创建时间";
t.bga.remark = "备注";
t.bga.view = "查看";
t.bga.annotate = "标注";
t.bga.loading = "加载中...";
t.bga.urlAddress = "URL地址";
t.bga.segmentedResultData = "分割后结果数据";

t.bga.Loading = "拼命加载中...";
t.bga.modelSelect = "模型选择";
t.bga.comparePreview = "标注预览";
t.bga.latitude = "维度";
t.bga.longitude = "经度";
t.bga.updateDate = "更新时间";
t.bga.deleteDate = "删除时间";
t.bga.creator = "创建者";
t.bga.updator = "更新者";

t.bga.samplingAt = "采样时间";
// home
t.bga.monitoringArea = "监测地区";
t.bga.dataTotal = "数据总量";
t.bga.todayUpload = "今日上传数据量";
t.bga.todayTagged = "今日已标注数据量";

// 浮游植物
t.phytoplankto = {};
t.phytoplankto.chineseName = "中文名";
t.phytoplankto.latinName = "拉丁名";
t.phytoplankto.englishName = "英文名";
t.phytoplankto.phytoplanktotonDataId = "浮游植物数据Id";
t.phytoplankto.sampleDataId = "样品采样数据Id";
t.phytoplankto.sampleDataInfo = "样品采样信息";
t.phytoplankto.samplingLocation = "采样地点";
t.phytoplankto.category = "门类";
t.phytoplankto.species = "种类";
t.phytoplankto.wetWeight = "湿重(10-4 mg)";
t.phytoplankto.count = "个数";
t.phytoplankto.cellCount = "细胞数(cells/L)";
t.phytoplankto.biomass = "生物量(mg/L)";
t.phytoplankto.samplingId = "样品采样Id";
t.phytoplankto.sampleVolumeLiters = "样品体积(L)";
t.phytoplankto.concentratedVolume = "浓缩后体积(mL)";
t.phytoplankto.dilutionFactor = "稀释倍数";
t.phytoplankto.fieldOfViewCount = "视野数(个)";
t.phytoplankto.fieldArea = "视野面积(mm2)";
t.phytoplankto.countingArea = "计数面积(mm2)";
t.phytoplankto.countingFrameVolume = "计数框体积(mL) ";
t.phytoplankto.countingFrameArea = "计数框面积(mm2)";
t.phytoplankto.samplingAt = "采样时间";
t.phytoplankto.name = "浮游植物名称";
t.phytoplankto.phylumName = "浮游植物门";
t.phytoplankto.className = "浮游植物纲";
t.phytoplankto.nameOrder = "浮游植物目";
t.phytoplankto.nameFamily = "浮游植物科";
t.phytoplankto.nameGenus = "浮游植物属";
t.phytoplankto.type = "浮游植物种";
t.phytoplankto.identification = "鉴定特征";
t.phytoplankto.stationId = "监测站点";
t.phytoplankto.stationAddr = "监测站点地址";
t.phytoplankto.autecology = "生态习性";
t.phytoplankto.contributor = "申请人/贡献者";
t.phytoplankto.reviewer = "审核人";
t.phytoplankto.status = "数据状态"; //，0：初始提交审核 1：审核通过 2：驳回
t.phytoplankto.images = "图片";
t.phytoplankto.vedios = "视频";
t.phytoplankto.attachment = "附件";
t.phytoplankto.description = "描述";
t.phytoplankto.uploadDataTable = "上传数据表";
t.phytoplankto.pleaseDownloadTemplate = "请先下载模板";
t.phytoplankto.downloadTemplate = "下载模板";
t.phytoplankto.uploadSuccess = "上传成功";
t.phytoplankto.uploadError = "数据表重复，请重新上传";
t.phytoplankto.createDate = "创建时间";
t.phytoplankto.dataSource = "数据来源";

t.phytoplankto.test1 = "样品编号（组合）";
t.phytoplankto.test2 = "测站代码";
t.phytoplankto.test3 = "测点名称";
t.phytoplankto.test4 = "年";
t.phytoplankto.test5 = "月";
t.phytoplankto.test6 = "日";
t.phytoplankto.test7 = "详细时间";
t.phytoplankto.test8 = "采样体积";
t.phytoplankto.test9 = "浓缩体积";
t.phytoplankto.test10 = "稀释倍数";
t.phytoplankto.test11 = "视野个数";
t.phytoplankto.test12 = "视野面积";
t.phytoplankto.test13 = "藻类门";
t.phytoplankto.test14 = "藻类纲";
t.phytoplankto.test15 = "藻类目";
t.phytoplankto.test16 = "藻类科";
t.phytoplankto.test17 = "藻类属";
t.phytoplankto.test18 = "藻类种类";
t.phytoplankto.test19 = "拉丁学名";
t.phytoplankto.test20 = "湿重";
t.phytoplankto.test21 = "个数";
t.phytoplankto.test22 = "细胞数";
t.phytoplankto.test23 = "生物量";

t.phytoplankto.stationCode = "站点编码";
t.phytoplankto.stationName = "站点名称";
t.phytoplankto.ammoniaNitrogen = "氨氮";
t.phytoplankto.totalPhosphorus = "总磷";
t.phytoplankto.monitorTime = "监测时间";
t.phytoplankto.monitorArea = "监测区域";
t.phytoplankto.ph = "PH";
t.phytoplankto.waterTemperature = "水温";
t.phytoplankto.algalDensity = "藻密度";
t.phytoplankto.permanganateIndex = "高锰酸盐指数";
t.phytoplankto.chlorophyllA = "叶绿素a";
t.phytoplankto.totalNitrogen = "总氮";
t.phytoplankto.turbidity = "浊度";
t.phytoplankto.conductivity = "电导率";
t.phytoplankto.dissolvedOxygen = "溶解氧";

//智慧滇池数据
t.zhdc = {};
t.zhdc.stationCode = "站点编码";
t.zhdc.stationName = "站点名称";
t.zhdc.ammoniaNitrogen = "氨氮";
t.zhdc.totalPhosphorus = "总磷";
t.zhdc.monitorTime = "监测时间";
t.zhdc.monitorArea = "监测区域";
t.zhdc.ph = "PH";
t.zhdc.waterTemperature = "水温";
t.zhdc.algalDensity = "藻密度";
t.zhdc.permanganateIndex = "高锰酸盐指数";
t.zhdc.chlorophyllA = "叶绿素a";
t.zhdc.totalNitrogen = "总氮";
t.zhdc.turbidity = "浊度";
t.zhdc.conductivity = "电导率";
t.zhdc.dissolvedOxygen = "溶解氧";
t.zhdc.transparency = "透明度";
t.zhdc.chemicalOxygenDemand = "化学需氧量";
t.zhdc.belongingRiver = "所属河流";
t.zhdc.biochemicalOxygenDemand = "生化需氧量";
t.zhdc.totalPhosphorusContent = "总磷总量";
t.zhdc.totalNitrogenConcentration = "总氮浓度";
t.zhdc.chemicalOxygenDemandStatus = "化学需氧量状态";
t.zhdc.totalNitrogenContent = "总氮总量";
t.zhdc.totalChemicalOxygenDemand = "化学需氧量总量";
t.zhdc.phValueStatus = "PH值状态";
t.zhdc.chemicalOxygenDemandConcentration = "化学需氧量浓度";
t.zhdc.instantaneousFlowStateOfWastewater = "废水瞬时流量状态";
t.zhdc.ammoniaNitrogenConcentration = "氨氮浓度";
t.zhdc.totalInstantaneousFlowRateOfWastewater = "废水瞬时流量总量";
t.zhdc.phConcentration = "PH值浓度";
t.zhdc.totalPhosphorusStatus = "总磷状态";
t.zhdc.totalAmmoniaNitrogen = "氨氮总量";
t.zhdc.instantaneousFlowConcentrationOfWastewater = "废水瞬时流量浓度";
t.zhdc.waterTemperatureStatus = "水温状态";
t.zhdc.totalNitrogenStatus = "总氮状态";
t.zhdc.outletName = "排口名称";
t.zhdc.totalPhosphorusConcentration = "总磷浓度";
t.zhdc.ammoniaNitrogenStatus = "氨氮状态";
t.zhdc.longitude="经度";
t.zhdc.latitude="纬度";



// 监测站数据
t.monitoring = {};
t.monitoring.stcd = "数据来源代码";
t.monitoring.wystcd = "唯一代码";
t.monitoring.stnm = "测站名称";
t.monitoring.stlvl = "测站级别";
t.monitoring.bnnm = "干流位置名称";
t.monitoring.rvnm = "支流河流名称";
t.monitoring.eslo = "经度";
t.monitoring.ntla = "纬度";
t.monitoring.staddr = "测站地址";
t.monitoring.adcd = "行政区划码";
t.monitoring.wudcd = "地表水水功能区码";
t.monitoring.munit = "管理单位";
t.monitoring.msunit = "监测单位";
t.monitoring.mnfrq = "监测频次";
t.monitoring.atst = "自动监测"; //，1表示是，0表示否
t.monitoring.fndym = "建站年月"; //，时间数据类型
t.monitoring.endym = "撤站年月"; //，时间数据类型
t.monitoring.remarks = "备注"; //，用于描述测站信息或保存超级链接

// 底栖动物
t.benthos = {};
t.benthos.zoobtId = "底栖动物Id";
t.benthos.nameCn = "中文名";
t.benthos.nameLatin = "拉丁名";
t.benthos.creator = "创建者";
t.benthos.updater = "更新者";
t.benthos.zoobtName = "底栖动物名称";
t.benthos.stationCode = "测站代码(站点ID)";
t.benthos.stationName = "测站名称(站点名称)";
t.benthos.averageBiomass = "平均生物量";
t.benthos.averageDensity = "平均密度";
t.benthos.biomass = "生物量(g/m2)";
t.benthos.latitudeAndLongitude = "经纬度";
t.benthos.samplingTime = "采样时间";
t.benthos.samplingLocation = "采样地点(湖体/流域)";
t.benthos.samplingTool = "采样工具";
t.benthos.zoobtSize = "底栖动物尺寸、大小(cm)";
t.benthos.zoobtPhylumName = "底栖动物门名称";
t.benthos.zoobtPhylumLatinName = "底栖动物门-拉丁名";
t.benthos.zoobtClassName = "底栖动物纲名称";
t.benthos.zoobtClassLatinName = "底栖动物纲-拉丁文";
t.benthos.zoobtOrderName = "底栖动物目名称";
t.benthos.zoobtOrderLatinName = "底栖动物目-拉丁名";
t.benthos.zoobtFamilyName = "底栖动物科名称";
t.benthos.zoobtFamilyLatinName = "底栖动物科-拉丁名";
t.benthos.zoobtGenusName = "底栖动物属名称";
t.benthos.zoobtGenusLatinName = "底栖动物属-拉丁名";
t.benthos.zoobtSpeciesName = "底栖动物种名称";
t.benthos.zoobtSpeciesLatinName = "底栖动物种-拉丁名";
t.benthos.samplingId = "样品采样Id";
t.benthos.sampleVolumeLiters = "样品体积(L)";
t.benthos.sampleDataId = "样品采样数据Id";
t.benthos.sampleDataInfo = "样品采样信息";
t.benthos.samplingLocation = "采样地点";
t.benthos.category = "门类";
t.benthos.species = "种类";
t.benthos.wetWeight = "湿重(10-4 mg)";
t.benthos.count = "个数";
t.benthos.cellCount = "细胞数(cells/L)";
t.benthos.biomass = "生物量(mg/L)";
t.benthos.samplingId = "样品采样Id";
t.benthos.sampleVolumeLiters = "样品体积(L)";
t.benthos.concentratedVolume = "浓缩后体积(mL)";
t.benthos.dilutionFactor = "稀释倍数";
t.benthos.fieldOfViewCount = "视野数(个)";
t.benthos.fieldArea = "视野面积(mm2)";
t.benthos.countingArea = "计数面积(mm2)";
t.benthos.countingFrameVolume = "计数框体积(mL) ";
t.benthos.countingFrameArea = "计数框面积(mm2)";
t.benthos.samplingAt = "采样时间";
t.benthos.latinName = "拉丁名";
t.benthos.uploadDataTable = "上传数据表";
t.benthos.pleaseDownloadTemplate = "请先下载模板";
t.benthos.downloadTemplate = "下载模板";
t.benthos.uploadSuccess = "上传成功";
t.benthos.density = "密度(ind./m2)";
t.benthos.imageData = "图片数据(存储URL)";
t.benthos.videoData = "视频数据(存储URL)";
t.benthos.createdAt = "创建时间";
t.benthos.updateAt = "更新时间";
t.benthos.deleteAt = "删除时间";
t.benthos.remark = "备注";
t.benthos.waterbodyName = "水体名称";
t.benthos.monitorName = "监测站点名称";
t.benthos.monitorUnit = "监测单位";
t.benthos.timeYear = "年";
t.benthos.timeMonth = "月";
t.benthos.timeDay = "日";
t.benthos.timeDetail = "具体时间";
t.benthos.zoobtPhylum = "门类";
t.benthos.zoobtClass = "纲";
t.benthos.zoobtOrder = "目";
t.benthos.zoobtFamily = "科";
t.benthos.zoobtGenus = "属";
t.benthos.zoobtName = "物种名称";
t.benthos.zoobtLatin = "拉丁学名";
t.benthos.density = "密度(个/m^2)";
t.benthos.wetWeight = "湿重(g/个)";
t.benthos.biomass = "生物量(g/m2)";
t.benthos.notes = "备注";

// 水文信息
t.hydrological = {};
t.hydrological.stationCode = "测站代码";
t.hydrological.waterTemperature = "水温(℃)";
t.hydrological.waterLevel = "水位(m)";
t.hydrological.reservoirCapacity = "库容";
t.hydrological.inflowRate = "进水流量(m3/s)";
t.hydrological.outflowRate = "出水流量(m3/s)";
t.hydrological.waterDepth = "水深";
t.hydrological.uswl = "闸上水位(m)";
t.hydrological.dswl = "闸下水位(m)";
t.hydrological.flowVelocity = "流速(m/s)";
t.hydrological.flowRate = "流量(m3/s)";
t.hydrological.sedimentConcentration = "含沙量(kg/m3)";
t.hydrological.sedimentTransport = "输沙量(t/m3/d)";
t.hydrological.flowMeasMethod = "测流方法";
t.hydrological.remark = "备注";
t.hydrological.createdAt = "创建时间";

// 水质信息
t.waterquality = {};
t.waterquality.stationCode = "测站代码";
t.waterquality.verticalLineNum = "垂线编号";
t.waterquality.layerNum = "层面编号";
t.waterquality.totalHardness = "总硬度(mg/L)";
t.waterquality.totalAlkalinity = "总碱度";
t.waterquality.totalOrganicCarbon = "总有机碳(mg/L)";
t.waterquality.dissolvedOrganicCarbon = "溶解性有机碳(mg/L)";
t.waterquality.chemicalOxygenDemand = "化学需氧量(mg/L)";
t.waterquality.totalPhosphorus = "总磷(mg/L)";
t.waterquality.solublePhosphate = "可溶性磷酸盐(mg/L)";
t.waterquality.totalNitrogen = "总氮(mg/L)";
t.waterquality.ammoniaNitrogen = "氨氮(mg/L)";
t.waterquality.nitrite = "亚硝酸盐(mg/L)";
t.waterquality.nitrate = "硝酸盐(mg/L)";
t.waterquality.permanganateIndex = "高锰酸盐指数(mg/L)";
t.waterquality.bod5 = "五日生化需氧量(mg/L)";
t.waterquality.copper = "铜(mg/L)";
t.waterquality.zinc = "锌(mg/L)";
t.waterquality.arsenic = "砷(mg/L)";
t.waterquality.mercury = "汞(mg/L)";
t.waterquality.cadmium = "镉(mg/L)";
t.waterquality.hexavalentChromium = "铬(六价)(mg/L)";
t.waterquality.lead = "铅(mg/L)";
t.waterquality.iron = "铁(mg/L)";
t.waterquality.manganese = "锰(mg/L)";
t.waterquality.nickel = "镍(mg/L)";
t.waterquality.selenium = "硒(mg/L)";
t.waterquality.fluoride = "氟化物(mg/L)";
t.waterquality.totalCyanide = "总氰化物(mg/L)";
t.waterquality.sulfide = "硫化物(mg/L)";
t.waterquality.chloride = "氯化物(mg/L)";
t.waterquality.petroleum = "石油类(mg/L)";
t.waterquality.sulfate = "硫酸盐(mg/L)";
t.waterquality.anionicSurfactant = "阴离子表面活性剂(mg/L)";
t.waterquality.pcbs = "多氯联苯(mg/L)";
t.waterquality.pahs = "多环芳烃(mg/L)";
t.waterquality.volatilePhenols = "挥发酚(mg/L)";
t.waterquality.fecalColiforms = "粪大肠菌群(ind./L)";
t.waterquality.microcystinToxin = "微囊藻毒素(μg/L)";
t.waterquality.chlorophyllA = "叶绿素a(mg/L)";
t.waterquality.waterTemperature = "水温(℃)";
t.waterquality.ph = "pH 值";
t.waterquality.electricalConductivity = "电导率 (μs/cm)";
t.waterquality.turbidity = "浊度 (NTU)";
t.waterquality.dissolvedOxygen = "溶解氧 (mg/L)";
t.waterquality.transparency = "透明度 (m)";
t.waterquality.orp = "氧化还原电位 (V)";
t.waterquality.underwaterIllumination = "水下光照 (mol)";
t.waterquality.suspendedSolidsConcentration = "悬浮物浓度 (mg/L)";
t.waterquality.createdAt = "创建时间";
t.waterquality.remark = "备注";


// 气象信息
t.weather = {};
t.weather.stationCode = "测站代码";
t.weather.windSpeed = "风速(m/s)";
t.weather.windDirection = "风向(°)";
t.weather.temperature = "气温(℃)";
t.weather.atmosphericPressure = "气压(Pa)";
t.weather.rainfall = "降雨量(mm)";
t.weather.par = "光合有效辐射(μmol/m2/s)";
t.weather.relativeHumidity = "相对湿度(%)";
t.weather.solarRadiation = "太阳辐射(w/m2)";
t.weather.createdAt = "创建时间";
t.weather.remark = "备注";

// 浮游动物
t.zooplankton = {};
t.zooplankton.nameCn = "中文名";
t.zooplankton.nameLatin = "拉丁名";
t.zooplankton.creator = "创建者";
t.zooplankton.updater = "更新者";
t.zooplankton.zptId = "浮游动物Id";
t.zooplankton.zptName = "浮游动物名称";
t.zooplankton.phytoplanktotonDataId = "浮游植物数据Id";
t.zooplankton.sampleDataId = "样品采样数据Id";
t.zooplankton.sampleDataInfo = "样品采样信息";
t.zooplankton.samplingLocation = "采样地点";
t.zooplankton.category = "门类";
t.zooplankton.species = "种类";
t.zooplankton.count = "个数";
t.zooplankton.cellCount = "细胞数(cells/L)";
t.zooplankton.biomass = "生物量(mg/L)";
t.zooplankton.samplingId = "样品采样Id";
t.zooplankton.sampleVolumeLiters = "样品体积(L)";
t.zooplankton.concentratedVolume = "浓缩后体积(mL)";
t.zooplankton.dilutionFactor = "稀释倍数";
t.zooplankton.fieldOfViewCount = "视野数(个)";
t.zooplankton.fieldArea = "视野面积(mm2)";
t.zooplankton.countingArea = "计数面积(mm2)";
t.zooplankton.countingFrameVolume = "计数框体积(mL) ";
t.zooplankton.countingFrameArea = "计数框面积(mm2)";
t.zooplankton.samplingAt = "采样时间";
t.zooplankton.latinName = "拉丁名";
t.zooplankton.uploadDataTable = "上传数据表";
t.zooplankton.pleaseDownloadTemplate = "请先下载模板";
t.zooplankton.downloadTemplate = "下载模板";
t.zooplankton.uploadSuccess = "上传成功";
t.zooplankton.uploadError = "数据表重复，请重新上传";
t.zooplankton.stationCode = "测站代码(站点ID)";
t.zooplankton.stationName = "测站名称(站点名称)";
t.zooplankton.zptCategoryName = "浮游动物类别名称";
t.zooplankton.zptCategoryLatinName = "浮游动物的类别拉丁名";
t.zooplankton.zptGenusName = "浮游动物的属名称";
t.zooplankton.zptGenusLatinName = "浮游动物的属拉丁名";
t.zooplankton.zptSpeciesName = "浮游动物的种加词名称";
t.zooplankton.zptSpeciesLatinName = "浮游动物的种加词拉丁名";
t.zooplankton.zptCount = "浮游动物数量";
t.zooplankton.samplingVolume = "采样体积(L)";
t.zooplankton.individualDensity = "个体密度(ind/L)";
t.zooplankton.individualFreshWeight = "个体鲜重(mg)";
t.zooplankton.remoteSensingData = "遥感数据(存储URL)";
t.zooplankton.imageData = "图片数据(存储URL)";
t.zooplankton.videoData = "视频数据(存储URL)";
t.zooplankton.createdAt = "创建时间";
t.zooplankton.updateAt = "更新时间";
t.zooplankton.deleteAt = "删除时间";
t.zooplankton.remark = "备注";
t.zooplankton.waterbodyName = "水体名称";
t.zooplankton.monitorName = "监测站点名称";
t.zooplankton.monitorUnit = "监测单位";
t.zooplankton.timeYear = "年";
t.zooplankton.timeMonth = "月";
t.zooplankton.timeDay = "日";
t.zooplankton.timeDetail = "具体时间";
t.zooplankton.zptPhylum = "门类";
t.zooplankton.zptClass = "纲";
t.zooplankton.zptOrder = "目";
t.zooplankton.zptFamily = "科";
t.zooplankton.zptGenus = "属";
t.zooplankton.zptName = "物种名称";
t.zooplankton.zptLatin = "拉丁学名";
t.zooplankton.density = "密度(个/m^2)";
t.zooplankton.wetWeight = "湿重(g/个)";
t.zooplankton.biomass = "生物量(g/m2)";
t.zooplankton.notes = "备注";

//大泊口数据
t.dbk = {};
t.dbk.monitorTime = "监测时间";
t.dbk.windSpeed = "风速(m/s)";
t.dbk.accumulatedRainfall = "雨量累计(mm)";
t.dbk.atmosphericTemperature = "大气温度(℃)";
t.dbk.digitalAirPressure = "数字气压(hPa)";
t.dbk.liquidLevel = "液位(mm)";
t.dbk.evaporation = "蒸发(mm)";
t.dbk.windDirection = "风向(°)";
t.dbk.atmosphericHumidity = "大气湿度(%RH)";
t.dbk.tbqTotalRadiation = "TBQ总辐射(W/m²)";
t.dbk.lightIntensity = "光照强度(Lux)";
t.dbk.cumulativeRadiation = "辐射累计(MJ/m²)";
t.dbk.conductivity = "电导率(us/cm)";
t.dbk.chlorophyll = "叶绿素(ug/L)";
t.dbk.turbidity = "浊度(NTU)";
t.dbk.totalNitrogen = "总氮(mg/L)";
t.dbk.phosphate = "磷酸盐(mg/L)";
t.dbk.totalPhosphorus = "总磷(mg/L)";
t.dbk.ammoniaNitrogen = "氨氮(mg/L)";
t.dbk.permanganateIndex = "高锰酸盐指数(mg/L)";
t.dbk.waterTemperature = "水温(℃)";
t.dbk.dissolvedOxygen = "溶解氧(mg/L)";
t.dbk.nitrate = "硝酸盐(mg/L)";
t.dbk.chemicalOxygenDemand = "化学需氧量(mg/L)";



t.router = {};
t.router = {
  主页: "主页",
  工作台: "工作台",
  水文信息: "水文信息",
  湖库水文数据: "湖库水文数据",
  堰闸水文数据: "堰闸水文数据",
  干支流水文数据: "干支流水文数据",
  底栖动物: "底栖动物",
  底栖动物详细表: "底栖动物详细表",
  底栖动物门: "底栖动物门",
  底栖动物纲: "底栖动物纲",
  底栖动物目: "底栖动物目",
  底栖动物科: "底栖动物科",
  底栖动物属: "底栖动物属",
  底栖动物种: "底栖动物种",
  底栖动物样品采样表: "底栖动物样品采样表",
  底栖动物测试数据表: "底栖动物测试数据表",
  底栖动物样品统计表: "底栖动物样品统计表",
  底栖动物Excel数据表导入: "底栖动物Excel数据表导入",
  底栖动物湖体:"底栖动物湖体",
  底栖动物河道:"底栖动物河道",
  底栖动物生态修复示范区:"底栖动物生态修复示范区(大泊口)",
  气象信息: "气象信息",
  浮游动物: "浮游动物",
  浮游动物密度: "浮游动物密度",
  浮游动物生物量: "浮游动物生物量",
  浮游动物数据资源: "浮游动物数据资源",
  浮游动物名录: "浮游动物名录",
  浮游动物数量: "浮游动物数量",
  浮游动物详情表: "浮游动物详情表",
  浮游动物门: "浮游动物门",
  浮游动物纲: "浮游动物纲",
  浮游动物目: "浮游动物目",
  浮游动物科: "浮游动物科",
  浮游动物属: "浮游动物属",
  浮游动物种: "浮游动物种",
  浮游动物类: "浮游动物类",
  浮游动物样品采样表: "浮游动物样品采样表",
  浮游动物测试数据表: "浮游动物测试数据表",
  浮游动物样品统计表: "浮游动物样品统计表",
  浮游动物Excel数据表导入: "浮游动物Excel数据表导入",
  浮游动物湖体:"浮游动物湖体",
  浮游动物河道:"浮游动物河道",
  浮游动物水库:"浮游动物水库",
  浮游动物生态修复示范区:"浮游动物生态修复示范区(大泊口)",
  水环境数据:"水环境数据",
  监测站基本信息表湖体:"监测站基本信息表(湖体)",
  监测站基本信息表河道:"监测站基本信息表(河道)",
  监测站基本信息表水质净化厂:"监测站基本信息表(水质净化厂)",
  鸟类测试数据:"鸟类测试数据",
  鸟类AI数据:"鸟类AI数据",
  鱼类AI数据:"鱼类AI数据",
  鸟类图像视频数据:"鸟类图像视频数据",
  浮游植物湖体野外站:"浮游植物湖体野外站",
  河道国控点:"河道国控点",
  河道生态补偿站点:"河道生态补偿站点",
  湖体自动监测:"湖体自动监测",
  湖体人工监测:"湖体人工监测",
  上传:"上传",
  图片上传:"图片上传",
  视频上传:"视频上传",
  大泊口:"大泊口",
  野外站:"野外站",
  湿地:"湿地",
  生态修复示范区大泊口实时监测:"生态修复示范区（大泊口）实时监测",
  生态修复示范区大泊口人工监测:"生态修复示范区（大泊口）人工监测",
  藻类:"藻类",
  浮游植物生态修复示范区大泊口:"浮游植物生态修复示范区（大泊口）",
  浮游动物生态修复示范区大泊口:"浮游动物生态修复示范区（大泊口）",
  底栖动物生态修复示范区大泊口:"底栖动物生态修复示范区（大泊口）",
  大型水生植物:"大型水生植物",
  大型水生植物生态修复示范区大泊口:"大型水生植物生态修复示范区（大泊口）",
  浮游生物AI数据:"浮游生物AI数据",
  底栖动物野外站:"底栖动物野外站",
  大泊口野外站水质数据:"大泊口野外站水质数据",
  浮游动物湖体野外站:"浮游动物湖体野外站",


/**
 * 沉积物观测
 */
  沉积物观测:"沉积物观测",
  全湖监测:"全湖监测",
  河道监测:"河道监测",
  湖滨湿地监测:"湖滨湿地监测",
  /**
   * 水文观测
   */
  水文观测:"水文观测",
  外海水位库容:"外海水位、库容",
  海口闸出流:"海水闸出流",
  草海水位库容:"草海水位、库容",
  西园隧道出流:"西园隧道出流",
  入滇河流生态补偿监测断面:"入滇河流生态补偿监测断面",
  /**
   * 气象观测
   */
  气象观测:"气象观测",
  草海昆明站:"草海昆明站",
  滇池野外站大泊口:"滇池野外站（大泊口）",

  大型水生植物湖体:"大型水生植物湖体",
  大型水生植物生态修复示范区:"大型水生植物生态修复示范区(大泊口)",
  鱼类湖体监测:"鱼类湖体监测",
  蓝藻数据管理: "蓝藻数据管理",
  无人机图片管理: "无人机图片管理",
  图片标注预览: "图片标注预览",
  地址管理: "地址管理",
  浮游植物: "浮游植物",
  浮游植物门: "浮游植物门",
  浮游植物纲: "浮游植物纲",
  浮游植物目: "浮游植物目",
  浮游植物科: "浮游植物科",
  浮游植物属: "浮游植物属",
  浮游植物详情表: "浮游植物详情表",
  浮游植物样品统计表: "浮游植物样品统计表",
  浮游植物数据表: "浮游植物数据表",
  水环境监测信息表野外站: "水环境监测信息表(野外站)",
  水环境监测信息表国控: "水环境监测信息表(国控)",
  浮游植物Excel数据导入: "浮游植物Excel数据导入",
  水生生物观测:"水生生物观测",
  浮游植物湖体:"浮游植物湖体",
  浮游植物河道:"浮游植物河道",
  浮游植物水库:"浮游植物水库",
  浮游植物生态修复示范区:"浮游植物生态修复示范区(大泊口)",
  浮游植物最新测试表: "浮游植物最新测试表",
  水质观测:"水质观测",
  藻类水华遥感监测:"藻类水华遥感监测",
  藻类水华富集区专项监测:"藻类水华富集区专项监测",
  蓝藻打捞湖体:"蓝藻打捞湖体",
  湖体:"湖体",
  河道:"河道",
  河道生态补偿站点实时监测数据: "河道生态补偿站点实时监测数据",
  河道国控点实时监测数据: "河道国控点实时监测数据",
  湖体水质国采人工监测数据: "湖体水质国采人工监测数据",
  水质净化厂出水口监测数据: "水质净化厂出水口监测数据",
  湖体水质自动监测数据: "湖体水质自动监测数据",
  湖体国控点人工监测:"湖体国控点人工监测",
  湖体国控点在线监测:"湖体国控点在线监测",
  湖体滇池野外站人工监测:"湖体滇池野外站人工监测",
  河道国控点实时监测:"河道国控点实时监测",
  河道生态补偿监测:"河道生态补偿监测",
  河道滇池野外站人工监测:"河道滇池野外站人工监测",
  湖滨湿地人工监测:"湖滨湿地人工监测",
  上游水库滇池野外站人工监测:"上游水库滇池野外站人工监测",
  生态修复示范区实时监测:"生态修复示范区（大泊口）实时监测",
  生态修复示范区人工监测:"生态修复示范区（大泊口）人工监测",
  浮游植物样品采样表: "浮游植物样品采样表",
  智慧滇池接口数据: "智慧滇池接口数据",
  浮游植物测试数据表: "浮游植物测试数据表",
  浮游植物物种分类表: "浮游植物物种分类表",
  水质信息: "水质信息",
  水质化学指标信息: "水质化学指标信息",
  水质物理指标信息: "水质物理指标信息",
  监测站: "监测站",
  监测站基本信息表: "监测站基本信息表",
  权限管理: "权限管理",
  用户管理: "用户管理",
  部门管理: "部门管理",
  角色管理: "角色管理",
  系统设置: "系统设置",
  菜单管理: "菜单管理",
  参数管理: "参数管理",
  字典管理: "字典管理",
  定时任务: "定时任务",
  文件上传: "文件上传",
  日志管理: "日志管理",
  登录日志: "登录日志",
  操作日志: "操作日志",
  异常日志: "异常日志"

};

t.schedule = {};
t.schedule.beanName = "bean名称";
t.schedule.beanNameTips = "spring bean名称, 如: testTask";
t.schedule.pauseBatch = "暂停";
t.schedule.resumeBatch = "恢复";
t.schedule.runBatch = "执行";
t.schedule.log = "日志列表";
t.schedule.params = "参数";
t.schedule.cronExpression = "cron表达式";
t.schedule.cronExpressionTips = "如: 0 0 12 * * ?";
t.schedule.remark = "备注";
t.schedule.status = "状态";
t.schedule.status0 = "暂停";
t.schedule.status1 = "正常";
t.schedule.statusLog0 = "失败";
t.schedule.statusLog1 = "成功";
t.schedule.pause = "暂停";
t.schedule.resume = "恢复";
t.schedule.run = "执行";
t.schedule.jobId = "任务ID";
t.schedule.times = "耗时(单位: 毫秒)";
t.schedule.createDate = "执行时间";

t.oss = {};
t.oss.config = "云存储配置";
t.oss.upload = "上传文件";
t.oss.url = "URL地址";
t.oss.urlRes = "结果URL地址";
t.oss.createDate = "创建时间";
t.oss.type = "类型";
t.oss.type1 = "七牛";
t.oss.type2 = "阿里云";
t.oss.type3 = "腾讯云";
t.oss.qiniuDomain = "域名";
t.oss.qiniuDomainTips = "七牛绑定的域名";
t.oss.qiniuPrefix = "路径前缀";
t.oss.qiniuPrefixTips = "不设置默认为空";
t.oss.qiniuAccessKey = "AccessKey";
t.oss.qiniuAccessKeyTips = "七牛AccessKey";
t.oss.qiniuSecretKey = "SecretKey";
t.oss.qiniuSecretKeyTips = "七牛SecretKey";
t.oss.qiniuBucketName = "空间名";
t.oss.qiniuBucketNameTips = "七牛存储空间名";
t.oss.aliyunDomain = "域名";
t.oss.aliyunDomainTips = "阿里云绑定的域名";
t.oss.aliyunPrefix = "路径前缀";
t.oss.aliyunPrefixTips = "不设置默认为空";
t.oss.aliyunEndPoint = "EndPoint";
t.oss.aliyunEndPointTips = "阿里云EndPoint";
t.oss.aliyunAccessKeyId = "AccessKeyId";
t.oss.aliyunAccessKeyIdTips = "阿里云AccessKeyId";
t.oss.aliyunAccessKeySecret = "AccessKeySecret";
t.oss.aliyunAccessKeySecretTips = "阿里云AccessKeySecret";
t.oss.aliyunBucketName = "BucketName";
t.oss.aliyunBucketNameTips = "阿里云BucketName";
t.oss.qcloudDomain = "域名";
t.oss.qcloudDomainTips = "腾讯云绑定的域名";
t.oss.qcloudPrefix = "路径前缀";
t.oss.qcloudPrefixTips = "不设置默认为空";
t.oss.qcloudAppId = "AppId";
t.oss.qcloudAppIdTips = "腾讯云AppId";
t.oss.qcloudSecretId = "SecretId";
t.oss.qcloudSecretIdTips = "腾讯云SecretId";
t.oss.qcloudSecretKey = "SecretKey";
t.oss.qcloudSecretKeyTips = "腾讯云SecretKey";
t.oss.qcloudBucketName = "BucketName";
t.oss.qcloudBucketNameTips = "腾讯云BucketName";
t.oss.qcloudRegion = "所属地区";
t.oss.qcloudRegionTips = "请选择";
t.oss.qcloudRegionBeijing1 = "北京一区(华北)";
t.oss.qcloudRegionBeijing = "北京";
t.oss.qcloudRegionShanghai = "上海(华东)";
t.oss.qcloudRegionGuangzhou = "广州(华南)";
t.oss.qcloudRegionChengdu = "成都(西南)";
t.oss.qcloudRegionChongqing = "重庆";
t.oss.qcloudRegionSingapore = "新加坡";
t.oss.qcloudRegionHongkong = "香港";
t.oss.qcloudRegionToronto = "多伦多";
t.oss.qcloudRegionFrankfurt = "法兰克福";

t.dept = {};
t.dept.name = "名称";
t.dept.parentName = "上级部门";
t.dept.sort = "排序";
t.dept.parentNameDefault = "一级部门";
t.dept.chooseerror = "请选择部门";
t.dept.title = "选择部门";

t.dict = {};
t.dict.dictName = "字典名称";
t.dict.dictType = "字典类型";
t.dict.dictLabel = "字典标签";
t.dict.dictSys = "字典管理";
t.dict.dictValue = "字典值";
t.dict.sort = "排序";
t.dict.remark = "备注";
t.dict.createDate = "创建时间";

t.logError = {};
t.logError.requestUri = "请求URI";
t.logError.requestMethod = "请求方式";
t.logError.requestParams = "请求参数";
t.logError.ip = "操作IP";
t.logError.userAgent = "用户代理";
t.logError.createDate = "创建时间";
t.logError.errorInfo = "异常信息";

t.logLogin = {};
t.logLogin.creatorName = "用户名";
t.logLogin.status = "状态";
t.logLogin.status0 = "失败";
t.logLogin.status1 = "成功";
t.logLogin.status2 = "账号已锁定";
t.logLogin.operation = "操作类型";
t.logLogin.operation0 = "登录";
t.logLogin.operation1 = "退出";
t.logLogin.ip = "操作IP";
t.logLogin.userAgent = "用户代理";
t.logLogin.createDate = "创建时间";

t.logOperation = {};
t.logOperation.status = "状态";
t.logOperation.status0 = "失败";
t.logOperation.status1 = "成功";
t.logOperation.creatorName = "用户名";
t.logOperation.operation = "用户操作";
t.logOperation.requestUri = "请求URI";
t.logOperation.requestMethod = "请求方式";
t.logOperation.requestParams = "请求参数";
t.logOperation.requestTime = "请求时长";
t.logOperation.ip = "操作IP";
t.logOperation.userAgent = "用户代理";
t.logOperation.createDate = "创建时间";

t.menu = {};
t.menu.name = "名称";
t.menu.icon = "图标";
t.menu.type = "类型";
t.menu.type0 = "菜单";
t.menu.type1 = "按钮";
t.menu.sort = "排序";
t.menu.url = "路由";
t.menu.openStyle = "打开方式";
t.menu.openStyle0 = "外部打开";
t.menu.openStyle1 = "内部打开";
t.menu.permissions = "授权标识";
t.menu.permissionsTips = "多个用逗号分隔,如:sys:menu:save,sys:menu:update";
t.menu.parentName = "上级菜单";
t.menu.parentNameDefault = "一级菜单";
t.menu.resource = "授权资源";
t.menu.resourceUrl = "资源URL";
t.menu.resourceMethod = "请求方式";
t.menu.resourceAddItem = "添加一项";

t.params = {};
t.params.paramCode = "编码";
t.params.paramValue = "值";
t.params.remark = "备注";

t.role = {};
t.role.name = "名称";
t.role.remark = "备注";
t.role.createDate = "创建时间";
t.role.menuList = "菜单授权";
t.role.deptList = "数据授权";

t.user = {};
t.user.username = "用户名";
t.user.deptName = "所属部门";
t.user.email = "邮箱";
t.user.mobile = "手机号";
t.user.status = "状态";
t.user.status0 = "停用";
t.user.status1 = "正常";
t.user.createDate = "创建时间";
t.user.password = "密码";
t.user.confirmPassword = "确认密码";
t.user.realName = "真实姓名";
t.user.gender = "性别";
t.user.gender0 = "男";
t.user.gender1 = "女";
t.user.gender2 = "保密";
t.user.roleIdList = "角色配置";
t.user.validate = {};
t.user.validate.confirmPassword = "确认密码与密码输入不一致";
t.user.select = "选择用户";
t.user.selecterror = "请选择一条记录";

export default t;
