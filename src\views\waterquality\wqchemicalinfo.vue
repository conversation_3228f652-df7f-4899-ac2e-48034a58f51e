<template>
  <div class="mod-bga__wqchemicalinfo">
    <el-form :inline="true" :model="state.dataForm" @keyup.enter="state.getDataList()">
      <el-form-item>
        <el-button v-if="state.hasPermission('waterquality:wqchemicalinfo:save')" type="primary" @click="addOrUpdateHandle()">{{ $t("add") }}</el-button>
      </el-form-item>
      <el-form-item>
        <el-button v-if="state.hasPermission('waterquality:wqchemicalinfo:delete')" type="danger" @click="state.deleteHandle()">{{ $t("delete") }}</el-button>
      </el-form-item>
    </el-form>
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border @selection-change="state.dataListSelectionChangeHandle" style="width: 100%">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="stationCode" :label="$t('waterquality.stationCode')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="verticalLineNum" :label="$t('waterquality.verticalLineNum')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="layerNum" :label="$t('waterquality.layerNum')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="totalHardness" :label="$t('waterquality.totalHardness')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="totalAlkalinity" :label="$t('waterquality.totalAlkalinity')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="totalOrganicCarbon" :label="$t('waterquality.totalOrganicCarbon')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="dissolvedOrganicCarbon" :label="$t('waterquality.dissolvedOrganicCarbon')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="chemicalOxygenDemand" :label="$t('waterquality.chemicalOxygenDemand')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="totalPhosphorus" :label="$t('waterquality.totalPhosphorus')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="solublePhosphate" :label="$t('waterquality.solublePhosphate')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="totalNitrogen" :label="$t('waterquality.totalNitrogen')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="ammoniaNitrogen" :label="$t('waterquality.ammoniaNitrogen')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="nitrite" :label="$t('waterquality.nitrite')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="nitrate" :label="$t('waterquality.nitrate')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="permanganateIndex" :label="$t('waterquality.permanganateIndex')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="bod5" :label="$t('waterquality.bod5')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="copper" :label="$t('waterquality.copper')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="zinc" :label="$t('waterquality.zinc')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="arsenic" :label="$t('waterquality.arsenic')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="mercury" :label="$t('waterquality.mercury')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="cadmium" :label="$t('waterquality.cadmium')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="hexavalentChromium" :label="$t('waterquality.hexavalentChromium')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="lead" :label="$t('waterquality.lead')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="iron" :label="$t('waterquality.iron')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="manganese" :label="$t('waterquality.manganese')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="nickel" :label="$t('waterquality.nickel')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="selenium" :label="$t('waterquality.selenium')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="fluoride" :label="$t('waterquality.fluoride')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="totalCyanide" :label="$t('waterquality.totalCyanide')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="sulfide" :label="$t('waterquality.sulfide')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="chloride" :label="$t('waterquality.chloride')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="petroleum" :label="$t('waterquality.petroleum')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="sulfate" :label="$t('waterquality.sulfate')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="anionicSurfactant" :label="$t('waterquality.anionicSurfactant')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="pcbs" :label="$t('waterquality.pcbs')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="pahs" :label="$t('waterquality.pahs')" header-align="center" align="center"></el-table-column>
      <el-table-column width="150" prop="volatilePhenols" :label="$t('waterquality.volatilePhenols')" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
        <template v-slot="scope">
          <el-button v-if="state.hasPermission('waterquality:wqchemicalinfo:update')" type="primary" link @click="addOrUpdateHandle(scope.row.id)">{{ $t("udpate") }}</el-button>
          <el-button v-if="state.hasPermission('waterquality:wqchemicalinfo:delete')" type="primary" link @click="state.deleteHandle(scope.row.id)">{{ $t("delete") }}</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination :current-page="state.page" :page-sizes="[10, 20, 50, 100]" :page-size="state.limit" :total="state.total" layout="total, sizes, prev, pager, next, jumper" @size-change="state.pageSizeChangeHandle" @current-change="state.pageCurrentChangeHandle"> </el-pagination>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="state.getDataList">{{ $t("confirm") }}</add-or-update>
  </div>
</template>

<script lang="ts" setup>
import useView from "@/hooks/useView";
import { reactive, ref, toRefs } from "vue";
import AddOrUpdate from "./wqchemicalinfo-add-or-update.vue";
import { globalLanguage } from "@/utils/globalLang";
const { $t } = globalLanguage();
const view = reactive({
  deleteIsBatch: true,
  getDataListURL: "/waterquality/wqchemicalinfo/page",
  getDataListIsPage: true,
  exportURL: "/waterquality/wqchemicalinfo/export",
  deleteURL: "/waterquality/wqchemicalinfo"
});

const state = reactive({ ...useView(view), ...toRefs(view) });

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
</script>
