<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-form-item label="站点编码" prop="stationCode">
        <el-input v-model="dataForm.stationCode" placeholder="站点编码"></el-input>
      </el-form-item>
      <el-form-item label="站点名称" prop="stationName">
        <el-input v-model="dataForm.stationName" placeholder="站点名称"></el-input>
      </el-form-item>
      <el-form-item label="数据类型(图片、视频、音频)" prop="dataType">
        <el-input v-model="dataForm.dataType" placeholder="数据类型(图片、视频、音频)"></el-input>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model="dataForm.description" placeholder="描述"></el-input>
      </el-form-item>
      <el-form-item label="数据来源" prop="dataSource">
        <el-input v-model="dataForm.dataSource" placeholder="数据来源"></el-input>
      </el-form-item>
      <el-form-item label="视频地址minio" prop="videoAddress">
        <el-input v-model="dataForm.videoAddress" placeholder="视频地址minio"></el-input>
      </el-form-item>
      <el-form-item label="标注地址" prop="labelAddress">
        <el-input v-model="dataForm.labelAddress" placeholder="标注地址"></el-input>
      </el-form-item>
      <el-form-item label="日期" prop="time">
        <el-input v-model="dataForm.time" placeholder="日期格式为YYYY-MM-DD"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
import { useRoute } from "vue-router";
const emit = defineEmits(["refreshDataList"]);
const visible = ref(false);
const dataFormRef = ref();
const route = useRoute();
const dataForm = reactive({
  id: "",
  stationCode: "",
  stationName: "",
  dataType: "",
  description: "",
  dataSource: "",
  videoAddress: "",
  labelAddress: "",
  time: ""
});

const rules = ref({
  stationCode: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  stationName: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  dataType: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  description: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  dataSource: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  videoAddress: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  labelAddress: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  time: [{ required: true, message: "必填项不能为空", trigger: "blur" }]
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/bga/videoupload/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/bga/videoupload", dataForm).then((res) => {
      ElMessage.success({
        message: "成功",
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
